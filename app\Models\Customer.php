<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'mobile',
        'email',
        'address',
        'city',
        'state',
        'pincode',
        'date_of_birth',
        'anniversary_date',
        'kyc_type',
        'kyc_number',
        'kyc_document_path',
        'total_purchases',
        'total_orders',
        'is_active',
        'notes',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'anniversary_date' => 'date',
        'total_purchases' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function estimates()
    {
        return $this->hasMany(Estimate::class);
    }

    public function repairs()
    {
        return $this->hasMany(Repair::class);
    }

    public function oldGoldPurchases()
    {
        return $this->hasMany(OldGoldPurchase::class);
    }

    public function savingSchemes()
    {
        return $this->hasMany(SavingScheme::class);
    }

    // Accessors & Mutators
    public function getFullAddressAttribute()
    {
        $parts = array_filter([$this->address, $this->city, $this->state, $this->pincode]);
        return implode(', ', $parts);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeWithBirthday($query, $date = null)
    {
        $date = $date ?: today();
        return $query->whereMonth('date_of_birth', $date->month)
                    ->whereDay('date_of_birth', $date->day);
    }

    public function scopeWithAnniversary($query, $date = null)
    {
        $date = $date ?: today();
        return $query->whereMonth('anniversary_date', $date->month)
                    ->whereDay('anniversary_date', $date->day);
    }
}
