<!-- Professional Header -->
<header class="header-glass sticky top-0 z-40">
    <!-- Metal Rates Ticker -->
    <div class="gold-gradient border-b border-yellow-300 border-opacity-30">
        <div class="px-6 py-2">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-6 overflow-hidden">
                    <div class="flex items-center space-x-2 text-yellow-900 font-semibold whitespace-nowrap">
                        <i class="fas fa-coins text-yellow-700"></i>
                        <span class="text-sm">Live Metal Rates</span>
                    </div>
                    
                    <div class="flex items-center space-x-6 ticker-scroll">
                        <div class="flex items-center space-x-2 text-yellow-800 whitespace-nowrap">
                            <i class="fas fa-circle text-green-600 text-xs"></i>
                            <span class="font-medium">Gold 24K:</span>
                            <span class="font-bold text-green-700">₹6,800/g</span>
                            <span class="text-xs text-green-600">↑ +50</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 text-yellow-800 whitespace-nowrap">
                            <i class="fas fa-circle text-green-600 text-xs"></i>
                            <span class="font-medium">Gold 22K:</span>
                            <span class="font-bold text-green-700">₹6,200/g</span>
                            <span class="text-xs text-green-600">↑ +45</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 text-yellow-800 whitespace-nowrap">
                            <i class="fas fa-circle text-green-600 text-xs"></i>
                            <span class="font-medium">Gold 18K:</span>
                            <span class="font-bold text-green-700">₹5,100/g</span>
                            <span class="text-xs text-green-600">↑ +35</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 text-yellow-800 whitespace-nowrap">
                            <i class="fas fa-circle text-blue-600 text-xs"></i>
                            <span class="font-medium">Silver 925:</span>
                            <span class="font-bold text-blue-700">₹85/g</span>
                            <span class="text-xs text-red-600">↓ -2</span>
                        </div>
                        
                        <div class="flex items-center space-x-2 text-yellow-800 whitespace-nowrap">
                            <i class="fas fa-circle text-blue-600 text-xs"></i>
                            <span class="font-medium">Silver 999:</span>
                            <span class="font-bold text-blue-700">₹90/g</span>
                            <span class="text-xs text-red-600">↓ -1</span>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4 text-yellow-800">
                    <div class="text-xs">
                        <span class="font-medium">Last Updated:</span>
                        <span x-text="currentTime"></span>
                    </div>
                    <button class="text-yellow-700 hover:text-yellow-900 transition-colors">
                        <i class="fas fa-sync-alt text-sm"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Header -->
    <div class="px-6 py-4">
        <div class="flex items-center justify-between">
            <!-- Left Section -->
            <div class="flex items-center space-x-4">
                <!-- Mobile Menu Button -->
                <button @click="toggleSidebar()" 
                        class="lg:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors">
                    <i class="fas fa-bars text-lg"></i>
                </button>
                
                <!-- Page Title -->
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">
                        <?php if(request()->routeIs('dashboard')): ?>
                            Dashboard Overview
                        <?php elseif(request()->routeIs('customers.*')): ?>
                            Customer Management
                        <?php elseif(request()->routeIs('products.*')): ?>
                            Product Inventory
                        <?php elseif(request()->routeIs('sales.*')): ?>
                            Sales & Billing
                        <?php elseif(request()->routeIs('estimates.*')): ?>
                            Estimates
                        <?php elseif(request()->routeIs('repairs.*')): ?>
                            Repairs & Services
                        <?php else: ?>
                            Jewel Pro
                        <?php endif; ?>
                    </h1>
                    <p class="text-sm text-gray-600 mt-1">
                        <?php if(request()->routeIs('dashboard')): ?>
                            Welcome back! Here's what's happening with your business today.
                        <?php elseif(request()->routeIs('customers.*')): ?>
                            Manage your customer relationships and profiles
                        <?php elseif(request()->routeIs('products.*')): ?>
                            Track and manage your jewelry inventory
                        <?php elseif(request()->routeIs('sales.*')): ?>
                            Process sales and manage billing
                        <?php elseif(request()->routeIs('estimates.*')): ?>
                            Create and manage customer estimates
                        <?php elseif(request()->routeIs('repairs.*')): ?>
                            Handle jewelry repairs and services
                        <?php else: ?>
                            Professional jewelry management system
                        <?php endif; ?>
                    </p>
                </div>
            </div>

            <!-- Right Section -->
            <div class="flex items-center space-x-4">
                <!-- Search -->
                <div class="relative hidden md:block">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" 
                           placeholder="Search customers, products..." 
                           class="w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
                </div>

                <!-- Quick Actions -->
                <div class="flex items-center space-x-2">
                    <button class="p-2 rounded-lg text-gray-600 hover:text-white hover:bg-green-500 transition-all duration-200 group">
                        <i class="fas fa-plus group-hover:rotate-90 transition-transform duration-200"></i>
                    </button>
                    
                    <button class="p-2 rounded-lg text-gray-600 hover:text-white hover:bg-blue-500 transition-all duration-200">
                        <i class="fas fa-calculator"></i>
                    </button>
                    
                    <button class="p-2 rounded-lg text-gray-600 hover:text-white hover:bg-purple-500 transition-all duration-200">
                        <i class="fas fa-chart-bar"></i>
                    </button>
                </div>

                <!-- Notifications -->
                <div class="relative">
                    <button class="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors relative">
                        <i class="fas fa-bell text-lg"></i>
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center notification-badge">3</span>
                    </button>
                </div>

                <!-- User Profile -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" 
                            class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                            <span class="text-white font-semibold text-sm"><?php echo e(substr(Auth::user()->name, 0, 1)); ?></span>
                        </div>
                        <div class="hidden md:block text-left">
                            <p class="text-sm font-medium text-gray-900"><?php echo e(Auth::user()->name); ?></p>
                            <p class="text-xs text-gray-600">Administrator</p>
                        </div>
                        <i class="fas fa-chevron-down text-gray-400 text-sm"></i>
                    </button>

                    <!-- Dropdown Menu -->
                    <div x-show="open" 
                         x-transition:enter="transition ease-out duration-100"
                         x-transition:enter-start="transform opacity-0 scale-95"
                         x-transition:enter-end="transform opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-75"
                         x-transition:leave-start="transform opacity-100 scale-100"
                         x-transition:leave-end="transform opacity-0 scale-95"
                         @click.away="open = false"
                         class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                        
                        <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-user w-4 h-4 mr-3"></i>
                            Profile Settings
                        </a>
                        
                        <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-cog w-4 h-4 mr-3"></i>
                            Account Settings
                        </a>
                        
                        <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-question-circle w-4 h-4 mr-3"></i>
                            Help & Support
                        </a>
                        
                        <hr class="my-2">
                        
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <i class="fas fa-sign-out-alt w-4 h-4 mr-3"></i>
                                Sign Out
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
<?php /**PATH C:\proj\jewel-pro\resources\views/layouts/components/header.blade.php ENDPATH**/ ?>