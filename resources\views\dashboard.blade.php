﻿<x-dashboard-layout>
    <div class="p-6 space-y-6">
        <!-- Welcome Section -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg p-6 text-white relative overflow-hidden">
            <div class="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -mr-16 -mt-16"></div>
            <div class="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-5 rounded-full -ml-12 -mb-12"></div>
            <div class="relative z-10 flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">Welcome back, {{ Auth::user()->name }}!</h1>
                    <p class="text-blue-100">Here's what's happening with your jewelry business today.</p>
                    <div class="mt-4 flex items-center space-x-4 text-sm">
                        <span class="flex items-center">
                            <i class="fas fa-calendar-day mr-2"></i>
                            {{ now()->format('l, F j, Y') }}
                        </span>
                        <span class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <span id="current-time">{{ now()->format('g:i A') }}</span>
                        </span>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center animate-pulse">
                        <i class="fas fa-gem text-white text-3xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Today's Sales</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">₹{{ number_format($stats['today_sales'] ?? 0, 0) }}</p>
                        <p class="text-xs text-green-600 mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>
                            @if(($stats['today_sales'] ?? 0) > 0) Active @else No sales yet @endif
                        </p>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-rupee-sign text-white text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Customers</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">{{ $stats['total_customers'] ?? 0 }}</p>
                        <p class="text-xs text-blue-600 mt-1">
                            <i class="fas fa-users mr-1"></i>
                            Registered customers
                        </p>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-users text-white text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Products in Stock</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">{{ $stats['total_products'] ?? 0 }}</p>
                        <p class="text-xs text-yellow-600 mt-1">
                            <i class="fas fa-gem mr-1"></i>
                            Available items
                        </p>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-gem text-white text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Pending Estimates</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">{{ $stats['pending_estimates'] ?? 0 }}</p>
                        <p class="text-xs text-orange-600 mt-1">
                            <i class="fas fa-clock mr-1"></i>
                            @if(($stats['pending_estimates'] ?? 0) > 0) Need attention @else All clear @endif
                        </p>
                    </div>
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-file-invoice text-white text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Statistics Row -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Pending Repairs</h3>
                    <i class="fas fa-tools text-gray-400"></i>
                </div>
                <div class="text-2xl font-bold text-gray-900">{{ $stats['pending_repairs'] ?? 0 }}</div>
                <p class="text-sm text-gray-600 mt-1">Items in service</p>
                @if(($stats['pending_repairs'] ?? 0) > 0)
                    <a href="{{ route('repairs.index') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">
                        View all repairs →
                    </a>
                @endif
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
                    <i class="fas fa-bolt text-gray-400"></i>
                </div>
                <div class="space-y-2">
                    <a href="{{ route('sales.create') }}" class="block w-full bg-green-50 hover:bg-green-100 text-green-700 font-medium py-2 px-3 rounded-lg text-sm transition-colors">
                        <i class="fas fa-plus mr-2"></i>New Sale
                    </a>
                    <a href="{{ route('customers.create') }}" class="block w-full bg-blue-50 hover:bg-blue-100 text-blue-700 font-medium py-2 px-3 rounded-lg text-sm transition-colors">
                        <i class="fas fa-user-plus mr-2"></i>Add Customer
                    </a>
                    <a href="{{ route('products.create') }}" class="block w-full bg-purple-50 hover:bg-purple-100 text-purple-700 font-medium py-2 px-3 rounded-lg text-sm transition-colors">
                        <i class="fas fa-gem mr-2"></i>Add Product
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">System Status</h3>
                    <i class="fas fa-heartbeat text-gray-400"></i>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Database</span>
                        <span class="flex items-center text-green-600 text-sm">
                            <i class="fas fa-circle text-xs mr-2"></i>Online
                        </span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Last Backup</span>
                        <span class="text-sm text-gray-500">{{ now()->format('M d, Y') }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Version</span>
                        <span class="text-sm text-gray-500">v1.0.0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity & Metal Rates -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Sales -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Sales</h3>
                    <a href="{{ route('sales.index') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">View all</a>
                </div>
                @if($recent_sales && $recent_sales->count() > 0)
                    <div class="space-y-3">
                        @foreach($recent_sales as $sale)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $sale->customer->name ?? 'Walk-in Customer' }}</p>
                                    <p class="text-sm text-gray-600">{{ $sale->sale_date->format('M d, Y') }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold text-gray-900">₹{{ number_format($sale->total_amount, 0) }}</p>
                                    <p class="text-xs text-gray-500">{{ $sale->payment_status }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-shopping-cart text-gray-300 text-4xl mb-3"></i>
                        <p class="text-gray-500">No recent sales</p>
                        <a href="{{ route('sales.create') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">
                            Create your first sale →
                        </a>
                    </div>
                @endif
            </div>

            <!-- Current Metal Rates -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Current Metal Rates</h3>
                    <a href="{{ route('metal-rates.index') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">Manage rates</a>
                </div>
                @if($metal_rates && $metal_rates->count() > 0)
                    <div class="space-y-3">
                        @foreach($metal_rates as $rate)
                            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $rate->metal_type }} ({{ $rate->purity }})</p>
                                    <p class="text-sm text-gray-600">Updated {{ $rate->effective_date->format('M d') }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold text-gray-900">₹{{ number_format($rate->rate_per_gram, 0) }}/g</p>
                                    <p class="text-xs text-green-600">
                                        <i class="fas fa-arrow-up mr-1"></i>Active
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-coins text-gray-300 text-4xl mb-3"></i>
                        <p class="text-gray-500">No metal rates configured</p>
                        <a href="{{ route('metal-rates.create') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2 inline-block">
                            Add metal rates →
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Real-time Clock Script -->
    <script>
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // Update time immediately and then every second
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</x-dashboard-layout>
