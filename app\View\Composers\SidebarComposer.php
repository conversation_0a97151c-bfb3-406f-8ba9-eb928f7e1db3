<?php

namespace App\View\Composers;

use Illuminate\View\View;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Sale;
use App\Models\Estimate;
use App\Models\Repair;

class SidebarComposer
{
    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        $stats = [
            'total_customers' => Customer::count(),
            'total_products' => Product::where('status', 'in_stock')->count(),
            'today_sales' => Sale::whereDate('sale_date', today())->sum('total_amount'),
            'pending_estimates' => Estimate::where('status', 'pending')->count(),
            'pending_repairs' => Repair::where('status', '!=', 'delivered')->count(),
        ];

        $view->with('stats', $stats);
    }
}
