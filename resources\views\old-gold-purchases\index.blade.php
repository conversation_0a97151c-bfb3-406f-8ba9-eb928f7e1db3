<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Old Gold Purchase & Exchange') }}
            </h2>
            @can('create_old_gold_purchase')
                <a href="{{ route('old-gold-purchases.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Record New Purchase
                </a>
            @endcan
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Search and Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('old-gold-purchases.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Search by purchase number, customer..." 
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <select name="metal_type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Metals</option>
                                <option value="Gold" {{ request('metal_type') === 'Gold' ? 'selected' : '' }}>Gold</option>
                                <option value="Silver" {{ request('metal_type') === 'Silver' ? 'selected' : '' }}>Silver</option>
                            </select>
                        </div>
                        <div>
                            <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Status</option>
                                <option value="purchased" {{ request('status') === 'purchased' ? 'selected' : '' }}>Purchased</option>
                                <option value="converted_to_voucher" {{ request('status') === 'converted_to_voucher' ? 'selected' : '' }}>Converted to Voucher</option>
                                <option value="used_in_exchange" {{ request('status') === 'used_in_exchange' ? 'selected' : '' }}>Used in Exchange</option>
                            </select>
                        </div>
                        <div>
                            <input type="date" name="from_date" value="{{ request('from_date') }}" 
                                   placeholder="From Date"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div class="flex space-x-2">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded flex-1">
                                Search
                            </button>
                            <a href="{{ route('old-gold-purchases.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Old Gold Purchases Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase #</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metal Details</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Weight</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($purchases as $purchase)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $purchase->purchase_number }}</div>
                                            <div class="text-sm text-gray-500">{{ $purchase->created_at->format('d M, Y') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $purchase->customer->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $purchase->customer->mobile }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $purchase->metal_type }} {{ $purchase->purity }}</div>
                                            @if($purchase->melting_loss_percentage > 0)
                                                <div class="text-sm text-red-600">Loss: {{ $purchase->melting_loss_percentage }}%</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">Gross: {{ $purchase->gross_weight }}g</div>
                                            <div class="text-sm text-gray-500">Final: {{ $purchase->final_weight }}g</div>
                                            @if($purchase->stone_weight > 0)
                                                <div class="text-xs text-purple-600">Stone: {{ $purchase->stone_weight }}g</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">₹{{ number_format($purchase->rate_per_gram, 2) }}/g</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">₹{{ number_format($purchase->total_amount, 2) }}</div>
                                            @if($purchase->voucher_amount > 0)
                                                <div class="text-sm text-green-600">Voucher: ₹{{ number_format($purchase->voucher_amount, 2) }}</div>
                                            @endif
                                            @if($purchase->cash_paid > 0)
                                                <div class="text-sm text-blue-600">Cash: ₹{{ number_format($purchase->cash_paid, 2) }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                {{ $purchase->status === 'purchased' ? 'bg-blue-100 text-blue-800' : 
                                                   ($purchase->status === 'converted_to_voucher' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800') }}">
                                                {{ ucfirst(str_replace('_', ' ', $purchase->status)) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                @can('view_old_gold_purchases')
                                                    <a href="{{ route('old-gold-purchases.show', $purchase) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                                @endcan
                                                @can('edit_old_gold_purchase')
                                                    <a href="{{ route('old-gold-purchases.edit', $purchase) }}" class="text-blue-600 hover:text-blue-900">Edit</a>
                                                @endcan
                                                @can('delete_old_gold_purchase')
                                                    @if($purchase->status !== 'used_in_exchange')
                                                        <form method="POST" action="{{ route('old-gold-purchases.destroy', $purchase) }}" class="inline" 
                                                              onsubmit="return confirm('Are you sure you want to delete this old gold purchase?')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                                        </form>
                                                    @endif
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                            No old gold purchases found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $purchases->links() }}
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            @if($purchases->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Total Purchases</div>
                            <div class="text-2xl font-bold text-gray-900">{{ $purchases->total() }}</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Total Amount</div>
                            <div class="text-2xl font-bold text-gray-900">₹{{ number_format($purchases->sum('total_amount'), 2) }}</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Total Weight</div>
                            <div class="text-2xl font-bold text-gray-900">{{ number_format($purchases->sum('final_weight'), 3) }}g</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Available Vouchers</div>
                            <div class="text-2xl font-bold text-green-600">₹{{ number_format($purchases->where('status', '!=', 'used_in_exchange')->sum('voucher_amount'), 2) }}</div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
