<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use <PERSON><PERSON><PERSON><PERSON>\Barcode\BarcodeGeneratorPNG;
use <PERSON><PERSON><PERSON><PERSON>\Barcode\BarcodeGeneratorHTML;
use <PERSON><PERSON><PERSON>er\Barcode\BarcodeGeneratorSVG;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;

class BarcodeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Temporarily disable permission checks for testing
        // $this->middleware('permission:view_products')->only(['index', 'show', 'generate']);
        // $this->middleware('permission:create_product')->only(['generateBulk', 'printLabels']);
    }

    /**
     * Display barcode management interface
     */
    public function index(Request $request)
    {
        $query = Product::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%")
                  ->orWhere('huid_number', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by metal type
        if ($request->filled('metal_type')) {
            $query->where('metal_type', $request->metal_type);
        }

        // Filter by barcode status
        if ($request->filled('barcode_status')) {
            if ($request->barcode_status === 'with_barcode') {
                $query->whereNotNull('barcode');
            } elseif ($request->barcode_status === 'without_barcode') {
                $query->whereNull('barcode');
            }
        }

        $products = $query->latest()->paginate(20);
        $categories = Product::distinct()->pluck('category')->filter();

        return view('barcodes.index', compact('products', 'categories'));
    }

    /**
     * Generate barcode for a single product
     */
    public function generate(Product $product, Request $request)
    {
        $format = $request->get('format', 'png');

        if (!$product->barcode) {
            return response()->json(['error' => 'Product does not have a barcode'], 400);
        }

        switch ($format) {
            case 'png':
                $generator = new BarcodeGeneratorPNG();
                $barcode = $generator->getBarcode($product->barcode, $generator::TYPE_CODE_128);
                return response($barcode)->header('Content-Type', 'image/png');

            case 'svg':
                $generator = new BarcodeGeneratorSVG();
                $barcode = $generator->getBarcode($product->barcode, $generator::TYPE_CODE_128);
                return response($barcode)->header('Content-Type', 'image/svg+xml');

            case 'html':
                $generator = new BarcodeGeneratorHTML();
                $barcode = $generator->getBarcode($product->barcode, $generator::TYPE_CODE_128);
                return response($barcode)->header('Content-Type', 'text/html');

            default:
                return response()->json(['error' => 'Invalid format'], 400);
        }
    }

    /**
     * Generate barcodes for multiple products
     */
    public function generateBulk(Request $request)
    {
        $validated = $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:products,id',
            'count' => 'required|integer|min:1|max:100',
        ]);

        $products = Product::whereIn('id', $validated['product_ids'])->get();
        $count = $validated['count'];

        return view('barcodes.bulk-generate', compact('products', 'count'));
    }

    /**
     * Print barcode labels
     */
    public function printLabels(Request $request)
    {
        $validated = $request->validate([
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:products,id',
            'count' => 'required|integer|min:1|max:100',
            'label_size' => 'required|in:small,medium,large',
            'include_price' => 'boolean',
            'include_weight' => 'boolean',
            'include_huid' => 'boolean',
        ]);

        $products = Product::whereIn('id', $validated['product_ids'])->get();
        $count = $validated['count'];
        $labelSize = $validated['label_size'];
        $includePrice = $validated['include_price'] ?? false;
        $includeWeight = $validated['include_weight'] ?? false;
        $includeHuid = $validated['include_huid'] ?? false;

        // Generate barcodes for PDF
        $generator = new BarcodeGeneratorPNG();
        $barcodeData = [];

        foreach ($products as $product) {
            if ($product->barcode) {
                $barcode = $generator->getBarcode($product->barcode, $generator::TYPE_CODE_128, 2, 50);
                $barcodeData[] = [
                    'product' => $product,
                    'barcode' => 'data:image/png;base64,' . base64_encode($barcode),
                    'count' => $count,
                ];
            }
        }

        $pdf = Pdf::loadView('barcodes.labels', compact(
            'barcodeData',
            'labelSize',
            'includePrice',
            'includeWeight',
            'includeHuid'
        ));

        return $pdf->download('barcode-labels-' . date('Y-m-d-H-i-s') . '.pdf');
    }

    /**
     * Scan barcode and find product
     */
    public function scan(Request $request)
    {
        $validated = $request->validate([
            'barcode' => 'required|string',
        ]);

        $product = Product::where('barcode', $validated['barcode'])->first();

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found with this barcode'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'barcode' => $product->barcode,
                'category' => $product->category,
                'metal_type' => $product->metal_type,
                'purity' => $product->purity,
                'net_weight' => $product->net_weight,
                'selling_price' => $product->selling_price,
                'quantity' => $product->quantity,
                'status' => $product->status,
                'huid_number' => $product->huid_number,
                'image_url' => $product->image_path ? asset('storage/' . $product->image_path) : null,
            ]
        ]);
    }

    /**
     * Generate new barcode for product
     */
    public function generateNewBarcode(Product $product)
    {
        // Generate new barcode if product doesn't have one
        if (!$product->barcode) {
            $product->barcode = $this->generateUniqueBarcode();
            $product->save();
        }

        return redirect()->route('barcodes.index')
            ->with('success', 'Barcode generated successfully for ' . $product->name);
    }

    /**
     * Generate barcodes for products without barcodes
     */
    public function generateMissingBarcodes()
    {
        $products = Product::whereNull('barcode')->get();
        $count = 0;

        foreach ($products as $product) {
            $product->barcode = $this->generateUniqueBarcode();
            $product->save();
            $count++;
        }

        return redirect()->route('barcodes.index')
            ->with('success', "Generated barcodes for {$count} products");
    }

    /**
     * Generate unique barcode
     */
    private function generateUniqueBarcode()
    {
        do {
            // Generate 13-digit barcode starting with 2 (for internal use)
            $barcode = '2' . str_pad(mt_rand(0, 999999999999), 12, '0', STR_PAD_LEFT);
        } while (Product::where('barcode', $barcode)->exists());

        return $barcode;
    }

    /**
     * Barcode scanner interface
     */
    public function scanner()
    {
        return view('barcodes.scanner');
    }
}
