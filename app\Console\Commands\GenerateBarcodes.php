<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;

class GenerateBarcodes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'barcodes:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate barcodes for products that don\'t have them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $products = Product::whereNull('barcode')->get();

        if ($products->isEmpty()) {
            $this->info('All products already have barcodes.');
            return;
        }

        $this->info("Found {$products->count()} products without barcodes.");

        $bar = $this->output->createProgressBar($products->count());
        $bar->start();

        foreach ($products as $product) {
            $product->barcode = Product::generateUniqueBarcode();
            $product->save();
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Generated barcodes for {$products->count()} products.");
    }
}
