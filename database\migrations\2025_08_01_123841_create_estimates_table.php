<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('estimates', function (Blueprint $table) {
            $table->id();
            $table->string('estimate_number')->unique();
            $table->foreignId('customer_id')->constrained('customers');
            $table->date('estimate_date');
            $table->date('valid_till');
            $table->decimal('subtotal', 15, 2);
            $table->decimal('total_tax', 10, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 15, 2);
            $table->boolean('rate_locked')->default(false);
            $table->enum('status', ['pending', 'approved', 'converted', 'expired'])->default('pending');
            $table->text('notes')->nullable();
            $table->foreignId('converted_to_sale_id')->nullable()->constrained('sales');
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('estimates');
    }
};
