<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Product Details')); ?> - <?php echo e($product->name); ?>

            </h2>
            <div class="flex space-x-2">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_product')): ?>
                    <a href="<?php echo e(route('products.edit', $product)); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit Product
                    </a>
                <?php endif; ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage_barcode')): ?>
                    <a href="<?php echo e(route('products.barcode', $product)); ?>" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Print Barcode
                    </a>
                <?php endif; ?>
                <a href="<?php echo e(route('products.index')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Inventory
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Product Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Product Image -->
                        <div class="lg:col-span-1">
                            <?php if($product->image_path): ?>
                                <img src="<?php echo e(asset('storage/' . $product->image_path)); ?>" alt="<?php echo e($product->name); ?>" 
                                     class="w-full h-64 object-cover rounded-lg shadow-md">
                            <?php else: ?>
                                <div class="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <span class="text-gray-500">No Image</span>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Status Badge -->
                            <div class="mt-4">
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                    <?php echo e($product->status === 'in_stock' ? 'bg-green-100 text-green-800' : 
                                       ($product->status === 'sold' ? 'bg-red-100 text-red-800' : 
                                       ($product->status === 'reserved' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'))); ?>">
                                    <?php echo e(ucfirst(str_replace('_', ' ', $product->status))); ?>

                                </span>
                            </div>
                        </div>

                        <!-- Product Details -->
                        <div class="lg:col-span-2">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Basic Info -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Product Name</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->name); ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Category</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->category); ?></dd>
                                        </div>
                                        <?php if($product->subcategory): ?>
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Subcategory</dt>
                                                <dd class="text-sm text-gray-900"><?php echo e($product->subcategory); ?></dd>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($product->size): ?>
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Size</dt>
                                                <dd class="text-sm text-gray-900"><?php echo e($product->size); ?></dd>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Quantity</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->quantity); ?></dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Metal Info -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Metal Information</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Metal Type</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->metal_type); ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Purity</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->purity); ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Gross Weight</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->gross_weight); ?> grams</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Net Weight</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->net_weight); ?> grams</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Wastage</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->wastage_percentage); ?>% (<?php echo e(number_format($product->wastage_amount, 3)); ?>g)</dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Stone Info -->
                                <?php if($product->has_stones): ?>
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 mb-4">Stone Information</h3>
                                        <dl class="space-y-2">
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Stone Weight</dt>
                                                <dd class="text-sm text-gray-900"><?php echo e($product->stone_weight); ?> carats</dd>
                                            </div>
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Stone Charges</dt>
                                                <dd class="text-sm text-gray-900">₹<?php echo e(number_format($product->stone_charges, 2)); ?></dd>
                                            </div>
                                        </dl>
                                    </div>
                                <?php endif; ?>

                                <!-- Pricing Info -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing Information</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Making Charges</dt>
                                            <dd class="text-sm text-gray-900">₹<?php echo e(number_format($product->making_charges, 2)); ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Cost Price</dt>
                                            <dd class="text-sm text-gray-900">₹<?php echo e(number_format($product->cost_price, 2)); ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Selling Price</dt>
                                            <dd class="text-sm text-gray-900 font-semibold">₹<?php echo e(number_format($product->selling_price, 2)); ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Profit</dt>
                                            <dd class="text-sm <?php echo e($product->profit >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                                ₹<?php echo e(number_format($product->profit, 2)); ?> (<?php echo e(number_format($product->profit_margin, 2)); ?>%)
                                            </dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Identification -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Identification</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Barcode</dt>
                                            <dd class="text-sm text-gray-900 font-mono"><?php echo e($product->barcode); ?></dd>
                                        </div>
                                        <?php if($product->huid_number): ?>
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">HUID Number</dt>
                                                <dd class="text-sm text-gray-900 font-mono"><?php echo e($product->huid_number); ?></dd>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">HSN Code</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->hsn_code); ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Added On</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->created_at->format('d M, Y h:i A')); ?></dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                            <dd class="text-sm text-gray-900"><?php echo e($product->updated_at->format('d M, Y h:i A')); ?></dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>

                            <?php if($product->description): ?>
                                <div class="mt-6">
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">Description</h3>
                                    <p class="text-sm text-gray-700"><?php echo e($product->description); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales History -->
            <?php if($product->saleItems->count() > 0): ?>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Sales History</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__currentLoopData = $product->saleItems->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $saleItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                <?php echo e($saleItem->sale->invoice_number); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e($saleItem->sale->customer->name); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e($saleItem->sale->sale_date->format('d M, Y')); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e($saleItem->quantity); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ₹<?php echo e(number_format($saleItem->item_total, 2)); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views/products/show.blade.php ENDPATH**/ ?>