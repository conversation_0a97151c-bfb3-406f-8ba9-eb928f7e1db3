@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Dashboard Styles */
@layer components {
    /* Sidebar Styles */
    .main-sidebar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .sidebar-brand {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .nav-item {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .nav-item:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateX(4px);
    }

    .nav-item.active {
        background: rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-item.active::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(to bottom, #fbbf24, #f59e0b);
    }

    .sidebar-scroll::-webkit-scrollbar {
        width: 4px;
    }

    .sidebar-scroll::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
    }

    .sidebar-scroll::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
    }

    .sidebar-scroll::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }

    /* Dashboard Card Animations */
    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    /* Gradient Backgrounds */
    .success-gradient {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    .gradient-bg {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }

    .gold-gradient {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    }

    .danger-gradient {
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    }

    /* Loading Animation */
    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
    }

    .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    /* Content Area */
    .content-area {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    /* Custom Scrollbar for main content */
    .content-area::-webkit-scrollbar {
        width: 6px;
    }

    .content-area::-webkit-scrollbar-track {
        background: #f1f5f9;
    }

    .content-area::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 3px;
    }

    .content-area::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }

    /* Modern Content Layout */
    .modern-content {
        padding: 1.5rem;
        min-height: calc(100vh - 200px);
    }

    .modern-content .content-card {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        border: 1px solid rgba(229, 231, 235, 0.8);
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .modern-content .content-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-1px);
    }

    /* Legacy Content Wrapper */
    .legacy-content-wrapper .py-12 {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

    .legacy-content-wrapper .max-w-7xl {
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .legacy-content-wrapper .sm\\:px-6 {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    .legacy-content-wrapper .lg\\:px-8 {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    /* Enhanced card styling for legacy pages */
    .legacy-content-wrapper .bg-white.shadow-sm,
    .legacy-content-wrapper .bg-white.overflow-hidden.shadow-sm {
        border-radius: 0.75rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        border: 1px solid rgba(229, 231, 235, 0.8);
        transition: all 0.3s ease;
    }

    .legacy-content-wrapper .bg-white.shadow-sm:hover,
    .legacy-content-wrapper .bg-white.overflow-hidden.shadow-sm:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-1px);
    }
}
