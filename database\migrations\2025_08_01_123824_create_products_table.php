<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('category'); // Ring, Necklace, Earrings, etc.
            $table->string('subcategory')->nullable(); // Gold Ring, Diamond Ring, etc.
            $table->string('metal_type'); // Gold, Silver, Platinum
            $table->string('purity'); // 22K, 18K, 14K, 925 Silver, etc.
            $table->decimal('gross_weight', 8, 3); // in grams
            $table->decimal('net_weight', 8, 3); // in grams
            $table->decimal('stone_weight', 8, 3)->default(0); // in carats
            $table->decimal('wastage_percentage', 5, 2)->default(0);
            $table->decimal('making_charges', 10, 2)->default(0);
            $table->decimal('stone_charges', 10, 2)->default(0);
            $table->string('hsn_code', 10)->default('71131900');
            $table->string('huid_number')->nullable()->unique();
            $table->string('barcode')->unique();
            $table->decimal('cost_price', 10, 2);
            $table->decimal('selling_price', 10, 2);
            $table->integer('quantity')->default(1);
            $table->string('size')->nullable();
            $table->text('description')->nullable();
            $table->string('image_path')->nullable();
            $table->boolean('has_stones')->default(false);
            $table->json('stone_details')->nullable(); // JSON for multiple stones
            $table->enum('status', ['in_stock', 'sold', 'reserved', 'repair'])->default('in_stock');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
