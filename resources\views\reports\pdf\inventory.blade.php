<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            color: #333;
        }
        .summary-cards {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        .summary-card {
            display: table-cell;
            width: 25%;
            padding: 15px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            text-align: center;
        }
        .summary-card .value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .summary-card .label {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .data-table .text-right {
            text-align: right;
        }
        .data-table .text-center {
            text-align: center;
        }
        .low-stock {
            background-color: #ffebee;
            color: #d32f2f;
        }
        .out-of-stock {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Jewel Pro') }}</div>
        <div class="report-title">Inventory Report</div>
        <div style="font-size: 14px; color: #666;">
            Generated on {{ date('d/m/Y H:i:s') }}
        </div>
    </div>

    <div class="summary-cards">
        <div class="summary-card">
            <div class="value">{{ $data['total_products'] ?? 0 }}</div>
            <div class="label">Total Products</div>
        </div>
        <div class="summary-card">
            <div class="value">₹{{ number_format($data['total_value'] ?? 0, 2) }}</div>
            <div class="label">Total Value</div>
        </div>
        <div class="summary-card">
            <div class="value">{{ $data['low_stock_count'] ?? 0 }}</div>
            <div class="label">Low Stock Items</div>
        </div>
        <div class="summary-card">
            <div class="value">{{ $data['out_of_stock_count'] ?? 0 }}</div>
            <div class="label">Out of Stock</div>
        </div>
    </div>

    @if(isset($data['products']) && count($data['products']) > 0)
    <table class="data-table">
        <thead>
            <tr>
                <th>Product Name</th>
                <th>Category</th>
                <th>Metal Type</th>
                <th class="text-right">Weight (g)</th>
                <th class="text-center">Stock</th>
                <th class="text-right">Cost Price</th>
                <th class="text-right">Selling Price</th>
                <th class="text-right">Total Value</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data['products'] as $product)
            <tr class="{{ $product->stock_quantity == 0 ? 'out-of-stock' : ($product->stock_quantity <= 5 ? 'low-stock' : '') }}">
                <td>{{ $product->name }}</td>
                <td>{{ $product->category }}</td>
                <td>{{ $product->metal_type }} {{ $product->purity }}</td>
                <td class="text-right">{{ number_format($product->weight, 3) }}</td>
                <td class="text-center">{{ $product->stock_quantity }}</td>
                <td class="text-right">₹{{ number_format($product->cost_price, 2) }}</td>
                <td class="text-right">₹{{ number_format($product->selling_price, 2) }}</td>
                <td class="text-right">₹{{ number_format($product->selling_price * $product->stock_quantity, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    @if(isset($data['category_summary']) && count($data['category_summary']) > 0)
    <h3>Category Summary</h3>
    <table class="data-table">
        <thead>
            <tr>
                <th>Category</th>
                <th class="text-center">Total Items</th>
                <th class="text-right">Total Value</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data['category_summary'] as $category)
            <tr>
                <td>{{ $category->category }}</td>
                <td class="text-center">{{ $category->total_items }}</td>
                <td class="text-right">₹{{ number_format($category->total_value, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>
    @endif

    <div class="footer">
        Report generated on {{ date('d/m/Y H:i:s') }}<br>
        {{ config('app.name', 'Jewel Pro') }} - Inventory Management System
    </div>
</body>
</html>
