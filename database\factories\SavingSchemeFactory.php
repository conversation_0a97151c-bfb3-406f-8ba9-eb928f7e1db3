<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Customer;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SavingScheme>
 */
class SavingSchemeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $schemeNames = [
            'Gold Saving Scheme',
            'Silver Saving Scheme',
            'Diamond Saving Scheme',
            'Wedding Collection Scheme',
            'Festival Special Scheme'
        ];

        $monthlyAmount = $this->faker->randomElement([1000, 2000, 3000, 5000, 10000, 15000, 20000]);
        $durationMonths = $this->faker->randomElement([6, 12, 18, 24, 36, 48, 60]);

        $startDate = $this->faker->dateTimeBetween('-2 years', '+1 month');
        $maturityDate = (clone $startDate)->modify("+{$durationMonths} months");

        $expectedTotal = $monthlyAmount * $durationMonths;
        $bonusAmount = $this->faker->randomFloat(2, 0, $expectedTotal * 0.1); // Up to 10% bonus
        $totalValue = $expectedTotal + $bonusAmount;

        // Calculate total paid based on scheme age and status
        $monthsElapsed = max(0, min($durationMonths, $startDate->diff(now())->m + ($startDate->diff(now())->y * 12)));
        $expectedPaid = $monthsElapsed * $monthlyAmount;

        $status = $this->faker->randomElement(['active', 'matured', 'closed']);

        if ($status === 'matured') {
            $totalPaid = $expectedTotal;
        } elseif ($status === 'closed') {
            $totalPaid = $this->faker->randomFloat(2, 0, $expectedTotal);
        } else {
            // Active scheme - might be behind, on track, or ahead
            $paymentVariation = $this->faker->randomFloat(2, -$monthlyAmount * 2, $monthlyAmount);
            $totalPaid = max(0, $expectedPaid + $paymentVariation);
        }

        return [
            'customer_id' => Customer::inRandomOrder()->first()?->id ?? Customer::factory(),
            'scheme_name' => $this->faker->randomElement($schemeNames),
            'monthly_amount' => $monthlyAmount,
            'duration_months' => $durationMonths,
            'start_date' => $startDate,
            'maturity_date' => $maturityDate,
            'total_paid' => $totalPaid,
            'bonus_amount' => $bonusAmount,
            'total_value' => $totalValue,
            'status' => $status,
            'auto_debit' => $this->faker->boolean(30),
            'notes' => $this->faker->optional(0.3)->sentence(),
            'created_by' => User::where('email', '<EMAIL>')->first()?->id ?? 1,
        ];
    }
}
