<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Old Gold Purchase Details') }} - {{ $oldGoldPurchase->purchase_number }}
            </h2>
            <div class="flex space-x-2">
                @can('edit_old_gold_purchase')
                    <a href="{{ route('old-gold-purchases.edit', $oldGoldPurchase) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit Purchase
                    </a>
                @endcan
                <a href="{{ route('old-gold-purchases.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Purchases
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Purchase Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Purchase Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Purchase Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Purchase Number</dt>
                                    <dd class="text-sm text-gray-900 font-mono">{{ $oldGoldPurchase->purchase_number }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Purchase Date</dt>
                                    <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->created_at->format('d M, Y h:i A') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created By</dt>
                                    <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->createdBy->name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="text-sm">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                            {{ $oldGoldPurchase->status === 'purchased' ? 'bg-blue-100 text-blue-800' : 
                                               ($oldGoldPurchase->status === 'converted_to_voucher' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800') }}">
                                            {{ ucfirst(str_replace('_', ' ', $oldGoldPurchase->status)) }}
                                        </span>
                                    </dd>
                                </div>
                                @if($oldGoldPurchase->usedInSale)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Used in Sale</dt>
                                        <dd class="text-sm text-blue-600">
                                            <a href="{{ route('sales.show', $oldGoldPurchase->usedInSale) }}" class="hover:text-blue-800">
                                                {{ $oldGoldPurchase->usedInSale->invoice_number }}
                                            </a>
                                        </dd>
                                    </div>
                                @endif
                            </dl>
                        </div>

                        <!-- Customer Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->customer->name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Mobile</dt>
                                    <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->customer->mobile }}</dd>
                                </div>
                                @if($oldGoldPurchase->customer->email)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                                        <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->customer->email }}</dd>
                                    </div>
                                @endif
                                @if($oldGoldPurchase->customer->address)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Address</dt>
                                        <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->customer->full_address }}</dd>
                                    </div>
                                @endif
                            </dl>
                        </div>
                    </div>

                    @if($oldGoldPurchase->notes)
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Notes</h3>
                            <p class="text-sm text-gray-700">{{ $oldGoldPurchase->notes }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Metal and Weight Details -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Metal Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Metal Information</h3>
                        <dl class="space-y-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Metal Type</dt>
                                <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->metal_type }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Purity</dt>
                                <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->purity }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Rate per Gram</dt>
                                <dd class="text-sm text-gray-900">₹{{ number_format($oldGoldPurchase->rate_per_gram, 2) }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Weight Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Weight Breakdown</h3>
                        <dl class="space-y-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Gross Weight</dt>
                                <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->gross_weight }} grams</dd>
                            </div>
                            @if($oldGoldPurchase->stone_weight > 0)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Stone Weight</dt>
                                    <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->stone_weight }} grams</dd>
                                </div>
                            @endif
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Net Weight</dt>
                                <dd class="text-sm text-gray-900">{{ $oldGoldPurchase->net_weight }} grams</dd>
                            </div>
                            @if($oldGoldPurchase->melting_loss_percentage > 0)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Melting Loss</dt>
                                    <dd class="text-sm text-red-600">{{ $oldGoldPurchase->melting_loss_percentage }}% ({{ $oldGoldPurchase->melting_loss_weight }} grams)</dd>
                                </div>
                            @endif
                            <div class="border-t pt-2">
                                <dt class="text-sm font-medium text-gray-500">Final Weight</dt>
                                <dd class="text-base font-bold text-gray-900">{{ $oldGoldPurchase->final_weight }} grams</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-gray-500">Total Amount</div>
                            <div class="text-2xl font-bold text-gray-900">₹{{ number_format($oldGoldPurchase->total_amount, 2) }}</div>
                            <div class="text-xs text-gray-500 mt-1">{{ $oldGoldPurchase->final_weight }}g × ₹{{ number_format($oldGoldPurchase->rate_per_gram, 2) }}/g</div>
                        </div>

                        @if($oldGoldPurchase->cash_paid > 0)
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="text-sm font-medium text-gray-500">Cash Paid</div>
                                <div class="text-2xl font-bold text-blue-600">₹{{ number_format($oldGoldPurchase->cash_paid, 2) }}</div>
                            </div>
                        @endif

                        @if($oldGoldPurchase->voucher_amount > 0)
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="text-sm font-medium text-gray-500">
                                    @if($oldGoldPurchase->status === 'used_in_exchange')
                                        Voucher Amount (Used)
                                    @else
                                        Available Voucher
                                    @endif
                                </div>
                                <div class="text-2xl font-bold {{ $oldGoldPurchase->status === 'used_in_exchange' ? 'text-gray-600' : 'text-green-600' }}">
                                    ₹{{ number_format($oldGoldPurchase->voucher_amount, 2) }}
                                </div>
                                @if($oldGoldPurchase->status !== 'used_in_exchange')
                                    <div class="text-xs text-green-600 mt-1">Can be used in future purchases</div>
                                @endif
                            </div>
                        @endif
                    </div>

                    <!-- Calculation Breakdown -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Calculation Breakdown</h4>
                        <div class="text-sm space-y-1">
                            <div class="flex justify-between">
                                <span>Gross Weight:</span>
                                <span>{{ $oldGoldPurchase->gross_weight }} grams</span>
                            </div>
                            @if($oldGoldPurchase->stone_weight > 0)
                                <div class="flex justify-between text-red-600">
                                    <span>Less: Stone Weight:</span>
                                    <span>{{ $oldGoldPurchase->stone_weight }} grams</span>
                                </div>
                            @endif
                            <div class="flex justify-between border-t pt-1">
                                <span>Net Weight:</span>
                                <span>{{ $oldGoldPurchase->net_weight }} grams</span>
                            </div>
                            @if($oldGoldPurchase->melting_loss_percentage > 0)
                                <div class="flex justify-between text-red-600">
                                    <span>Less: Melting Loss ({{ $oldGoldPurchase->melting_loss_percentage }}%):</span>
                                    <span>{{ $oldGoldPurchase->melting_loss_weight }} grams</span>
                                </div>
                            @endif
                            <div class="flex justify-between border-t pt-1 font-medium">
                                <span>Final Weight:</span>
                                <span>{{ $oldGoldPurchase->final_weight }} grams</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Rate per Gram:</span>
                                <span>₹{{ number_format($oldGoldPurchase->rate_per_gram, 2) }}</span>
                            </div>
                            <div class="flex justify-between border-t pt-1 font-bold text-lg">
                                <span>Total Amount:</span>
                                <span>₹{{ number_format($oldGoldPurchase->total_amount, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
