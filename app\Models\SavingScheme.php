<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class SavingScheme extends Model
{
    use HasFactory;

    protected $fillable = [
        'scheme_number',
        'customer_id',
        'scheme_name',
        'monthly_amount',
        'duration_months',
        'start_date',
        'maturity_date',
        'total_paid',
        'bonus_amount',
        'total_value',
        'status',
        'auto_debit',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'monthly_amount' => 'decimal:2',
        'total_paid' => 'decimal:2',
        'bonus_amount' => 'decimal:2',
        'total_value' => 'decimal:2',
        'start_date' => 'date',
        'maturity_date' => 'date',
        'auto_debit' => 'boolean',
    ];

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function payments()
    {
        return $this->hasMany(SchemePayment::class, 'saving_scheme_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Accessors
    public function getExpectedTotalAttribute()
    {
        return $this->monthly_amount * $this->duration_months;
    }

    public function getPendingAmountAttribute()
    {
        return $this->expected_total - $this->total_paid;
    }

    public function getCompletionPercentageAttribute()
    {
        if ($this->expected_total <= 0) return 0;
        return min(100, ($this->total_paid / $this->expected_total) * 100);
    }

    public function getIsMaturedAttribute()
    {
        return $this->maturity_date <= today();
    }

    public function getIsOverdueAttribute()
    {
        return $this->status === 'active' && $this->maturity_date < today() && $this->total_paid < $this->expected_total;
    }

    public function getMonthsCompletedAttribute()
    {
        if ($this->start_date > today()) return 0;
        return min($this->duration_months, $this->start_date->diffInMonths(today()) + 1);
    }

    public function getExpectedPaidAttribute()
    {
        return $this->months_completed * $this->monthly_amount;
    }

    public function getPaymentStatusAttribute()
    {
        if ($this->total_paid >= $this->expected_total) {
            return 'completed';
        } elseif ($this->total_paid >= $this->expected_paid) {
            return 'on_track';
        } else {
            return 'behind';
        }
    }

    public function getNextDueDateAttribute()
    {
        if ($this->status !== 'active') return null;

        $lastPayment = $this->payments()->latest('payment_date')->first();
        if (!$lastPayment) {
            return $this->start_date;
        }

        $nextDue = $lastPayment->payment_date->addMonth();
        return $nextDue <= $this->maturity_date ? $nextDue : null;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeMatured($query)
    {
        return $query->where('status', 'matured');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'active')
                    ->where('maturity_date', '<', today())
                    ->whereRaw('total_paid < (monthly_amount * duration_months)');
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    // Boot method to generate scheme number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($scheme) {
            if (empty($scheme->scheme_number)) {
                $scheme->scheme_number = 'SCH-' . date('Y') . '-' . str_pad(static::whereYear('created_at', date('Y'))->count() + 1, 6, '0', STR_PAD_LEFT);
            }
        });
    }
}
