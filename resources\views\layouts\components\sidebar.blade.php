<!-- Professional Sidebar -->
<div class="relative">
    <!-- Mobile Overlay -->
    <div x-show="sidebarOpen" 
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         @click="closeSidebar()"
         class="fixed inset-0 bg-gray-900 bg-opacity-75 z-40 lg:hidden"></div>

    <!-- Sidebar -->
    <div :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'"
         class="fixed inset-y-0 left-0 z-50 w-72 main-sidebar transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 flex flex-col">
        
        <!-- Brand Header -->
        <div class="sidebar-brand px-6 py-6 border-b border-white border-opacity-10">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-gem text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h1 class="text-xl font-bold text-white">{{ config('app.name', 'Jewel Pro') }}</h1>
                    <p class="text-sm text-blue-200">Professional Edition</p>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto sidebar-scroll">
            <!-- Dashboard -->
            <a href="{{ route('dashboard') }}" 
               class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                <span>Dashboard</span>
            </a>

            <!-- Sales & Orders Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">Sales & Orders</h3>
                
                <a href="{{ route('sales.index') }}" 
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('sales.*') ? 'active' : '' }}">
                    <i class="fas fa-shopping-cart w-5 h-5 mr-3"></i>
                    <span>Sales & Billing</span>
                    <span class="ml-auto bg-green-500 text-white text-xs px-2 py-1 rounded-full">New</span>
                </a>
                
                <a href="{{ route('estimates.index') }}" 
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('estimates.*') ? 'active' : '' }}">
                    <i class="fas fa-file-invoice w-5 h-5 mr-3"></i>
                    <span>Estimates</span>
                </a>
                
                <a href="{{ route('old-gold-exchange.index') }}" 
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('old-gold-exchange.*') ? 'active' : '' }}">
                    <i class="fas fa-exchange-alt w-5 h-5 mr-3"></i>
                    <span>Old Gold Exchange</span>
                </a>
            </div>

            <!-- Inventory Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">Inventory</h3>
                
                <a href="{{ route('products.index') }}" 
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('products.*') ? 'active' : '' }}">
                    <i class="fas fa-gem w-5 h-5 mr-3"></i>
                    <span>Products & Inventory</span>
                </a>
                
                <a href="{{ route('barcodes.index') }}"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('barcodes.*') ? 'active' : '' }}">
                    <i class="fas fa-barcode w-5 h-5 mr-3"></i>
                    <span>Barcodes & Labels</span>
                </a>
            </div>

            <!-- Services Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">Services</h3>
                
                <a href="{{ route('repairs.index') }}" 
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('repairs.*') ? 'active' : '' }}">
                    <i class="fas fa-tools w-5 h-5 mr-3"></i>
                    <span>Repairs & Services</span>
                </a>
                
                <a href="{{ route('saving-schemes.index') }}" 
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('saving-schemes.*') ? 'active' : '' }}">
                    <i class="fas fa-piggy-bank w-5 h-5 mr-3"></i>
                    <span>Saving Schemes</span>
                </a>
            </div>

            <!-- Customers Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">Customers</h3>
                
                <a href="{{ route('customers.index') }}" 
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('customers.*') ? 'active' : '' }}">
                    <i class="fas fa-users w-5 h-5 mr-3"></i>
                    <span>Customer Management</span>
                </a>
            </div>

            <!-- Tools Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">Tools</h3>
                
                <a href="{{ route('metal-rates.index') }}" 
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('metal-rates.*') ? 'active' : '' }}">
                    <i class="fas fa-coins w-5 h-5 mr-3"></i>
                    <span>Metal Rates</span>
                </a>
                
                <a href="{{ route('calculator') }}"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('calculator*') ? 'active' : '' }}">
                    <i class="fas fa-calculator w-5 h-5 mr-3"></i>
                    <span>Price Calculator</span>
                </a>
            </div>

            <!-- Analytics Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">Analytics</h3>
                
                <a href="{{ route('reports.index') }}" 
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('reports.*') ? 'active' : '' }}">
                    <i class="fas fa-chart-bar w-5 h-5 mr-3"></i>
                    <span>Reports & Analytics</span>
                </a>
            </div>

            <!-- System Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">System</h3>
                
                <a href="{{ route('settings.index') }}" 
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg {{ request()->routeIs('settings.*') ? 'active' : '' }}">
                    <i class="fas fa-cog w-5 h-5 mr-3"></i>
                    <span>Settings</span>
                </a>
            </div>
        </nav>

        <!-- User Profile Section -->
        <div class="border-t border-white border-opacity-10 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white font-semibold text-sm">{{ substr(Auth::user()->name, 0, 1) }}</span>
                    </div>
                </div>
                <div class="ml-3 flex-1 min-w-0">
                    <p class="text-sm font-medium text-white truncate">{{ Auth::user()->name }}</p>
                    <p class="text-xs text-blue-200 truncate">{{ Auth::user()->email }}</p>
                </div>
                <div class="ml-2">
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="text-blue-200 hover:text-white transition-colors">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
