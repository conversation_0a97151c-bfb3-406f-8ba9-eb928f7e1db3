<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class OldGoldPurchase extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_number',
        'customer_id',
        'metal_type',
        'purity',
        'gross_weight',
        'stone_weight',
        'net_weight',
        'melting_loss_percentage',
        'melting_loss_weight',
        'final_weight',
        'rate_per_gram',
        'total_amount',
        'voucher_amount',
        'cash_paid',
        'status',
        'used_in_sale_id',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'gross_weight' => 'decimal:3',
        'stone_weight' => 'decimal:3',
        'net_weight' => 'decimal:3',
        'melting_loss_percentage' => 'decimal:2',
        'melting_loss_weight' => 'decimal:3',
        'final_weight' => 'decimal:3',
        'rate_per_gram' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'voucher_amount' => 'decimal:2',
        'cash_paid' => 'decimal:2',
    ];

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function usedInSale()
    {
        return $this->belongsTo(Sale::class, 'used_in_sale_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Accessors
    public function getAvailableVoucherAmountAttribute()
    {
        if ($this->status === 'used_in_exchange') {
            return 0;
        }
        return $this->voucher_amount;
    }

    public function getIsUsedAttribute()
    {
        return $this->status === 'used_in_exchange';
    }

    // Scopes
    public function scopeAvailable($query)
    {
        return $query->where('status', '!=', 'used_in_exchange');
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    // Boot method to generate purchase number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($purchase) {
            if (empty($purchase->purchase_number)) {
                $purchase->purchase_number = 'OGP-' . date('Y') . '-' . str_pad(static::whereYear('created_at', date('Y'))->count() + 1, 6, '0', STR_PAD_LEFT);
            }
        });
    }
}
