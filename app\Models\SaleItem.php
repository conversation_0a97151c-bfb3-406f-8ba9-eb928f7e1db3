<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SaleItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'sale_id',
        'product_id',
        'quantity',
        'metal_rate',
        'making_charges',
        'stone_charges',
        'wastage_amount',
        'item_total',
        'cgst_rate',
        'sgst_rate',
        'igst_rate',
    ];

    protected $casts = [
        'metal_rate' => 'decimal:2',
        'making_charges' => 'decimal:2',
        'stone_charges' => 'decimal:2',
        'wastage_amount' => 'decimal:2',
        'item_total' => 'decimal:2',
        'cgst_rate' => 'decimal:2',
        'sgst_rate' => 'decimal:2',
        'igst_rate' => 'decimal:2',
    ];

    // Relationships
    public function sale()
    {
        return $this->belongsTo(Sale::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    // Accessors
    public function getCgstAmountAttribute()
    {
        return ($this->item_total * $this->cgst_rate) / 100;
    }

    public function getSgstAmountAttribute()
    {
        return ($this->item_total * $this->sgst_rate) / 100;
    }

    public function getIgstAmountAttribute()
    {
        return ($this->item_total * $this->igst_rate) / 100;
    }

    public function getTotalTaxAttribute()
    {
        return $this->cgst_amount + $this->sgst_amount + $this->igst_amount;
    }

    public function getTotalWithTaxAttribute()
    {
        return $this->item_total + $this->total_tax;
    }
}
