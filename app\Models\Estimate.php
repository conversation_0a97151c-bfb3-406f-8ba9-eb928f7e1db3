<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Estimate extends Model
{
    use HasFactory;

    protected $fillable = [
        'estimate_number',
        'customer_id',
        'estimate_date',
        'valid_till',
        'subtotal',
        'total_tax',
        'discount_amount',
        'total_amount',
        'rate_locked',
        'status',
        'notes',
        'converted_to_sale_id',
        'created_by',
    ];

    protected $casts = [
        'estimate_date' => 'date',
        'valid_till' => 'date',
        'subtotal' => 'decimal:2',
        'total_tax' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'rate_locked' => 'boolean',
    ];

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function estimateItems()
    {
        return $this->hasMany(EstimateItem::class);
    }

    public function convertedToSale()
    {
        return $this->belongsTo(Sale::class, 'converted_to_sale_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Accessors
    public function getIsExpiredAttribute()
    {
        return $this->valid_till < today();
    }

    public function getIsConvertedAttribute()
    {
        return $this->status === 'converted';
    }

    public function getCanConvertAttribute()
    {
        return in_array($this->status, ['pending', 'approved']) && !$this->is_expired;
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeExpired($query)
    {
        return $query->where('valid_till', '<', today());
    }

    public function scopeValid($query)
    {
        return $query->where('valid_till', '>=', today());
    }

    // Boot method to generate estimate number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($estimate) {
            if (empty($estimate->estimate_number)) {
                $estimate->estimate_number = 'EST-' . date('Y') . '-' . str_pad(static::whereYear('created_at', date('Y'))->count() + 1, 6, '0', STR_PAD_LEFT);
            }
        });
    }
}
