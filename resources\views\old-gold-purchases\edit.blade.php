<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Old Gold Purchase') }} - #{{ $oldGoldPurchase->purchase_number }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('old-gold-purchases.update', $oldGoldPurchase) }}" id="purchaseForm">
                        @csrf
                        @method('PUT')
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <!-- Customer Selection -->
                            <div>
                                <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer</label>
                                <select name="customer_id" id="customer_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="">Select Customer</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}" {{ $oldGoldPurchase->customer_id == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }} - {{ $customer->mobile }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('customer_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Purchase Date -->
                            <div>
                                <label for="purchase_date" class="block text-sm font-medium text-gray-700">Purchase Date</label>
                                <input type="date" name="purchase_date" id="purchase_date" value="{{ $oldGoldPurchase->purchase_date->format('Y-m-d') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                @error('purchase_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Payment Method -->
                            <div>
                                <label for="payment_method" class="block text-sm font-medium text-gray-700">Payment Method</label>
                                <select name="payment_method" id="payment_method" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="">Select Payment Method</option>
                                    <option value="cash" {{ $oldGoldPurchase->payment_method == 'cash' ? 'selected' : '' }}>Cash</option>
                                    <option value="bank_transfer" {{ $oldGoldPurchase->payment_method == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                    <option value="cheque" {{ $oldGoldPurchase->payment_method == 'cheque' ? 'selected' : '' }}>Cheque</option>
                                    <option value="exchange" {{ $oldGoldPurchase->payment_method == 'exchange' ? 'selected' : '' }}>Exchange</option>
                                </select>
                                @error('payment_method')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Payment Status -->
                            <div>
                                <label for="payment_status" class="block text-sm font-medium text-gray-700">Payment Status</label>
                                <select name="payment_status" id="payment_status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="pending" {{ $oldGoldPurchase->payment_status == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="partial" {{ $oldGoldPurchase->payment_status == 'partial' ? 'selected' : '' }}>Partial</option>
                                    <option value="paid" {{ $oldGoldPurchase->payment_status == 'paid' ? 'selected' : '' }}>Paid</option>
                                </select>
                                @error('payment_status')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Purchase Items -->
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Purchase Items</h3>
                                <button type="button" id="addItem" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    Add Item
                                </button>
                            </div>

                            <div id="itemsContainer">
                                @foreach($oldGoldPurchase->purchaseItems as $index => $item)
                                <div class="item-row border border-gray-200 rounded-lg p-4 mb-4">
                                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700">Item Description</label>
                                            <input type="text" name="items[{{ $index }}][item_description]" value="{{ $item->item_description }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Metal Type</label>
                                            <select name="items[{{ $index }}][metal_type]" class="metal-type mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                                <option value="">Select</option>
                                                <option value="Gold" {{ $item->metal_type == 'Gold' ? 'selected' : '' }}>Gold</option>
                                                <option value="Silver" {{ $item->metal_type == 'Silver' ? 'selected' : '' }}>Silver</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Purity</label>
                                            <select name="items[{{ $index }}][purity]" class="purity mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                                <option value="">Select</option>
                                                <option value="22K" {{ $item->purity == '22K' ? 'selected' : '' }}>22K</option>
                                                <option value="18K" {{ $item->purity == '18K' ? 'selected' : '' }}>18K</option>
                                                <option value="916" {{ $item->purity == '916' ? 'selected' : '' }}>916</option>
                                                <option value="999" {{ $item->purity == '999' ? 'selected' : '' }}>999</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Gross Weight (g)</label>
                                            <input type="number" name="items[{{ $index }}][gross_weight]" value="{{ $item->gross_weight }}" step="0.001" class="gross-weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Net Weight (g)</label>
                                            <input type="number" name="items[{{ $index }}][net_weight]" value="{{ $item->net_weight }}" step="0.001" class="net-weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        </div>
                                    </div>
                                    <div class="mt-4 flex justify-between items-center">
                                        <div class="text-sm text-gray-600">
                                            Rate: ₹<span class="rate-display">{{ number_format($item->rate_per_gram, 2) }}</span>/g | 
                                            Amount: ₹<span class="amount-display">{{ number_format($item->amount, 2) }}</span>
                                        </div>
                                        <button type="button" class="remove-item bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded">
                                            Remove
                                        </button>
                                    </div>
                                    <input type="hidden" name="items[{{ $index }}][rate_per_gram]" value="{{ $item->rate_per_gram }}" class="rate-input">
                                    <input type="hidden" name="items[{{ $index }}][amount]" value="{{ $item->amount }}" class="amount-input">
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea name="notes" id="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ $oldGoldPurchase->notes }}</textarea>
                            @error('notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Totals -->
                        <div class="border-t pt-6 mb-6">
                            <div class="flex justify-end">
                                <div class="w-64">
                                    <dl class="space-y-2">
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Total Weight</dt>
                                            <dd class="text-sm text-gray-900"><span id="totalWeight">{{ number_format($oldGoldPurchase->total_weight, 3) }}</span>g</dd>
                                        </div>
                                        <div class="flex justify-between border-t pt-2">
                                            <dt class="text-base font-medium text-gray-900">Total Amount</dt>
                                            <dd class="text-base font-medium text-gray-900">₹<span id="totalAmount">{{ number_format($oldGoldPurchase->total_amount, 2) }}</span></dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Amount Paid</dt>
                                            <dd class="text-sm text-gray-900">₹<span id="amountPaid">{{ number_format($oldGoldPurchase->amount_paid, 2) }}</span></dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Balance</dt>
                                            <dd class="text-sm text-gray-900">₹<span id="balance">{{ number_format($oldGoldPurchase->total_amount - $oldGoldPurchase->amount_paid, 2) }}</span></dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('old-gold-purchases.show', $oldGoldPurchase) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Purchase
                            </button>
                        </div>

                        <input type="hidden" name="total_weight" id="totalWeightInput" value="{{ $oldGoldPurchase->total_weight }}">
                        <input type="hidden" name="total_amount" id="totalAmountInput" value="{{ $oldGoldPurchase->total_amount }}">
                        <input type="hidden" name="amount_paid" id="amountPaidInput" value="{{ $oldGoldPurchase->amount_paid }}">
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Metal rates data
        const metalRates = @json($metalRates->keyBy(function($rate) { return $rate->metal_type . '-' . $rate->purity; }));
        let itemIndex = {{ count($oldGoldPurchase->purchaseItems) }};

        // Add item functionality
        document.getElementById('addItem').addEventListener('click', function() {
            const container = document.getElementById('itemsContainer');
            const itemHtml = `
                <div class="item-row border border-gray-200 rounded-lg p-4 mb-4">
                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Item Description</label>
                            <input type="text" name="items[${itemIndex}][item_description]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Metal Type</label>
                            <select name="items[${itemIndex}][metal_type]" class="metal-type mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                <option value="">Select</option>
                                <option value="Gold">Gold</option>
                                <option value="Silver">Silver</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Purity</label>
                            <select name="items[${itemIndex}][purity]" class="purity mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                <option value="">Select</option>
                                <option value="22K">22K</option>
                                <option value="18K">18K</option>
                                <option value="916">916</option>
                                <option value="999">999</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Gross Weight (g)</label>
                            <input type="number" name="items[${itemIndex}][gross_weight]" step="0.001" class="gross-weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Net Weight (g)</label>
                            <input type="number" name="items[${itemIndex}][net_weight]" step="0.001" class="net-weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            Rate: ₹<span class="rate-display">0.00</span>/g | 
                            Amount: ₹<span class="amount-display">0.00</span>
                        </div>
                        <button type="button" class="remove-item bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded">
                            Remove
                        </button>
                    </div>
                    <input type="hidden" name="items[${itemIndex}][rate_per_gram]" value="0" class="rate-input">
                    <input type="hidden" name="items[${itemIndex}][amount]" value="0" class="amount-input">
                </div>
            `;
            container.insertAdjacentHTML('beforeend', itemHtml);
            itemIndex++;
            attachEventListeners();
        });

        // Remove item functionality
        function attachEventListeners() {
            document.querySelectorAll('.remove-item').forEach(button => {
                button.addEventListener('click', function() {
                    this.closest('.item-row').remove();
                    calculateTotals();
                });
            });

            // Calculate amounts when inputs change
            document.querySelectorAll('.metal-type, .purity, .net-weight').forEach(input => {
                input.addEventListener('change', function() {
                    calculateItemAmount(this.closest('.item-row'));
                });
            });
        }

        function calculateItemAmount(row) {
            const metalType = row.querySelector('.metal-type').value;
            const purity = row.querySelector('.purity').value;
            const netWeight = parseFloat(row.querySelector('.net-weight').value) || 0;

            if (metalType && purity && netWeight > 0) {
                const rateKey = metalType + '-' + purity;
                const rate = metalRates[rateKey] ? metalRates[rateKey].rate_per_gram : 0;
                const amount = netWeight * rate;

                row.querySelector('.rate-display').textContent = rate.toFixed(2);
                row.querySelector('.amount-display').textContent = amount.toFixed(2);
                row.querySelector('.rate-input').value = rate;
                row.querySelector('.amount-input').value = amount;

                calculateTotals();
            }
        }

        function calculateTotals() {
            let totalWeight = 0;
            let totalAmount = 0;

            document.querySelectorAll('.item-row').forEach(row => {
                const netWeight = parseFloat(row.querySelector('.net-weight').value) || 0;
                const amount = parseFloat(row.querySelector('.amount-input').value) || 0;
                
                totalWeight += netWeight;
                totalAmount += amount;
            });

            document.getElementById('totalWeight').textContent = totalWeight.toFixed(3);
            document.getElementById('totalAmount').textContent = totalAmount.toFixed(2);

            document.getElementById('totalWeightInput').value = totalWeight;
            document.getElementById('totalAmountInput').value = totalAmount;

            // Update balance
            const amountPaid = parseFloat(document.getElementById('amountPaidInput').value) || 0;
            const balance = totalAmount - amountPaid;
            document.getElementById('balance').textContent = balance.toFixed(2);
        }

        // Initialize event listeners
        attachEventListeners();
    </script>
</x-app-layout>
