<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SavingScheme;
use App\Models\SchemePayment;

class SavingSchemeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 20 sample saving schemes
        $schemes = SavingScheme::factory(20)->create();

        // Create payments for some schemes
        foreach ($schemes as $scheme) {
            if ($scheme->status === 'active' && $scheme->total_paid > 0) {
                $this->createPaymentsForScheme($scheme);
            }
        }
    }

    private function createPaymentsForScheme(SavingScheme $scheme)
    {
        $totalPaid = $scheme->total_paid;
        $monthlyAmount = $scheme->monthly_amount;
        $currentDate = $scheme->start_date->copy();
        $remainingAmount = $totalPaid;

        while ($remainingAmount > 0 && $currentDate <= now()) {
            $paymentAmount = min($monthlyAmount, $remainingAmount);

            // Add some variation to payment dates (some might be late)
            $paymentDate = $currentDate->copy()->addDays(rand(-5, 15));
            $isLate = $paymentDate > $currentDate;
            $lateFee = $isLate ? rand(0, 100) : 0;

            SchemePayment::create([
                'saving_scheme_id' => $scheme->id,
                'amount' => $paymentAmount,
                'payment_date' => $paymentDate,
                'due_date' => $currentDate->copy(),
                'payment_method' => collect(['cash', 'card', 'upi', 'bank_transfer'])->random(),
                'transaction_reference' => $paymentDate->format('TXN') . rand(100000, 999999),
                'is_late' => $isLate,
                'late_fee' => $lateFee,
                'notes' => collect([null, 'Regular payment', 'Partial payment', 'Late payment'])->random(),
                'created_by' => 1,
            ]);

            $remainingAmount -= $paymentAmount;
            $currentDate->addMonth();
        }
    }
}
