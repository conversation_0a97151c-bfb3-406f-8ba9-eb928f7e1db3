<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Saving Schemes') }}
            </h2>
            @can('create_saving_scheme')
                <a href="{{ route('saving-schemes.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Create New Scheme
                </a>
            @endcan
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="text-sm font-medium text-gray-500">Active Schemes</div>
                        <div class="text-2xl font-bold text-blue-600">{{ $schemes->where('status', 'active')->count() }}</div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="text-sm font-medium text-gray-500">Matured Schemes</div>
                        <div class="text-2xl font-bold text-green-600">{{ $schemes->where('status', 'matured')->count() }}</div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="text-sm font-medium text-gray-500">Overdue Schemes</div>
                        <div class="text-2xl font-bold text-red-600">{{ $schemes->filter(fn($s) => $s->is_overdue)->count() }}</div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="text-sm font-medium text-gray-500">Total Collections</div>
                        <div class="text-2xl font-bold text-gray-900">₹{{ number_format($schemes->sum('total_paid'), 2) }}</div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('saving-schemes.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Search by scheme #, name, customer..." 
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="matured" {{ request('status') === 'matured' ? 'selected' : '' }}>Matured</option>
                                <option value="closed" {{ request('status') === 'closed' ? 'selected' : '' }}>Closed</option>
                                <option value="defaulted" {{ request('status') === 'defaulted' ? 'selected' : '' }}>Defaulted</option>
                            </select>
                        </div>
                        <div>
                            <select name="scheme_name" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Schemes</option>
                                @foreach($schemes->pluck('scheme_name')->unique() as $schemeName)
                                    <option value="{{ $schemeName }}" {{ request('scheme_name') === $schemeName ? 'selected' : '' }}>{{ $schemeName }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="overdue" value="1" {{ request('overdue') ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Overdue Only</span>
                            </label>
                        </div>
                        <div class="flex space-x-2">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded flex-1">
                                Search
                            </button>
                            <a href="{{ route('saving-schemes.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Schemes Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scheme Details</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount & Duration</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($schemes as $scheme)
                                    <tr class="{{ $scheme->is_overdue ? 'bg-red-50' : '' }}">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $scheme->scheme_number }}</div>
                                            <div class="text-sm text-gray-500">{{ $scheme->scheme_name }}</div>
                                            <div class="text-xs text-gray-400">Started: {{ $scheme->start_date->format('d M, Y') }}</div>
                                            @if($scheme->is_overdue)
                                                <div class="text-xs text-red-600 font-semibold">Overdue</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $scheme->customer->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $scheme->customer->mobile }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">₹{{ number_format($scheme->monthly_amount, 0) }}/month</div>
                                            <div class="text-sm text-gray-500">{{ $scheme->duration_months }} months</div>
                                            <div class="text-xs text-gray-400">Maturity: {{ $scheme->maturity_date->format('d M, Y') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                ₹{{ number_format($scheme->total_paid, 0) }} / ₹{{ number_format($scheme->expected_total, 0) }}
                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                                <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $scheme->completion_percentage }}%"></div>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1">{{ number_format($scheme->completion_percentage, 1) }}% complete</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                {{ $scheme->status === 'active' ? 'bg-blue-100 text-blue-800' : 
                                                   ($scheme->status === 'matured' ? 'bg-green-100 text-green-800' : 
                                                   ($scheme->status === 'closed' ? 'bg-gray-100 text-gray-800' : 'bg-red-100 text-red-800')) }}">
                                                {{ ucfirst($scheme->status) }}
                                            </span>
                                            @if($scheme->auto_debit)
                                                <div class="text-xs text-blue-600 mt-1">Auto Debit</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                @can('view_saving_schemes')
                                                    <a href="{{ route('saving-schemes.show', $scheme) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                                @endcan
                                                @can('manage_scheme_payments')
                                                    @if($scheme->status === 'active')
                                                        <a href="{{ route('saving-schemes.add-payment', $scheme) }}" class="text-green-600 hover:text-green-900">Add Payment</a>
                                                    @endif
                                                @endcan
                                                @can('edit_saving_scheme')
                                                    @if($scheme->status === 'active')
                                                        <a href="{{ route('saving-schemes.edit', $scheme) }}" class="text-blue-600 hover:text-blue-900">Edit</a>
                                                    @endif
                                                @endcan
                                                @if($scheme->status === 'matured')
                                                    <a href="{{ route('saving-schemes.certificate', $scheme) }}" class="text-purple-600 hover:text-purple-900">Certificate</a>
                                                @endif
                                                @can('delete_saving_scheme')
                                                    @if($scheme->payments->count() === 0)
                                                        <form method="POST" action="{{ route('saving-schemes.destroy', $scheme) }}" class="inline" 
                                                              onsubmit="return confirm('Are you sure you want to delete this scheme?')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                                        </form>
                                                    @endif
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            No saving schemes found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $schemes->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
