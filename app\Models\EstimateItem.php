<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EstimateItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'estimate_id',
        'item_name',
        'metal_type',
        'purity',
        'gross_weight',
        'net_weight',
        'stone_weight',
        'metal_rate',
        'making_charges',
        'stone_charges',
        'wastage_percentage',
        'wastage_amount',
        'item_total',
        'description',
    ];

    protected $casts = [
        'gross_weight' => 'decimal:3',
        'net_weight' => 'decimal:3',
        'stone_weight' => 'decimal:3',
        'metal_rate' => 'decimal:2',
        'making_charges' => 'decimal:2',
        'stone_charges' => 'decimal:2',
        'wastage_percentage' => 'decimal:2',
        'wastage_amount' => 'decimal:2',
        'item_total' => 'decimal:2',
    ];

    // Relationships
    public function estimate()
    {
        return $this->belongsTo(Estimate::class);
    }

    // Accessors
    public function getTotalWeightAttribute()
    {
        return $this->net_weight + $this->wastage_amount;
    }

    public function getMetalValueAttribute()
    {
        return $this->net_weight * $this->metal_rate;
    }
}
