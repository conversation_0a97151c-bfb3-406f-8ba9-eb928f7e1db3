<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Business Profile Settings') }}
            </h2>
            <a href="{{ route('settings.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Settings
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('settings.update-business-profile') }}" enctype="multipart/form-data">
                        @csrf

                        <!-- Business Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Business Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="business_name" class="block text-sm font-medium text-gray-700">Business Name *</label>
                                    <input type="text" name="business_name" id="business_name" value="{{ old('business_name', $settings['business_name']) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('business_name')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="established_year" class="block text-sm font-medium text-gray-700">Established Year</label>
                                    <input type="number" name="established_year" id="established_year" value="{{ old('established_year', $settings['established_year']) }}" 
                                           min="1900" max="{{ date('Y') }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('established_year')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div class="mt-6">
                                <label for="business_address" class="block text-sm font-medium text-gray-700">Business Address *</label>
                                <textarea name="business_address" id="business_address" rows="3" required
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('business_address', $settings['business_address']) }}</textarea>
                                @error('business_address')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="business_phone" class="block text-sm font-medium text-gray-700">Phone Number *</label>
                                    <input type="tel" name="business_phone" id="business_phone" value="{{ old('business_phone', $settings['business_phone']) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('business_phone')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="business_email" class="block text-sm font-medium text-gray-700">Email Address *</label>
                                    <input type="email" name="business_email" id="business_email" value="{{ old('business_email', $settings['business_email']) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('business_email')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div class="mt-6">
                                <label for="business_website" class="block text-sm font-medium text-gray-700">Website URL</label>
                                <input type="url" name="business_website" id="business_website" value="{{ old('business_website', $settings['business_website']) }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('business_website')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Legal Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Legal Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="gstin" class="block text-sm font-medium text-gray-700">GSTIN *</label>
                                    <input type="text" name="gstin" id="gstin" value="{{ old('gstin', $settings['gstin']) }}" required
                                           maxlength="15" placeholder="22**********1Z5"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('gstin')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="pan_number" class="block text-sm font-medium text-gray-700">PAN Number *</label>
                                    <input type="text" name="pan_number" id="pan_number" value="{{ old('pan_number', $settings['pan_number']) }}" required
                                           maxlength="10" placeholder="**********"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('pan_number')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div class="mt-6">
                                <label for="license_number" class="block text-sm font-medium text-gray-700">Business License Number</label>
                                <input type="text" name="license_number" id="license_number" value="{{ old('license_number', $settings['license_number']) }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('license_number')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Business Logo -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Business Logo</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="business_logo" class="block text-sm font-medium text-gray-700">Upload Logo</label>
                                    <input type="file" name="business_logo" id="business_logo" accept="image/*"
                                           class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                    <p class="mt-1 text-xs text-gray-500">Supported formats: JPEG, PNG, JPG. Max size: 2MB</p>
                                    @error('business_logo')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                @if($settings['business_logo'])
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Current Logo</label>
                                        <div class="mt-1">
                                            <img src="{{ asset('storage/' . $settings['business_logo']) }}" alt="Business Logo" class="h-20 w-auto border border-gray-300 rounded">
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Preview Section -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Business Card Preview</h3>
                            <div class="bg-gray-50 p-6 rounded-lg border">
                                <div class="flex items-start space-x-4">
                                    @if($settings['business_logo'])
                                        <img src="{{ asset('storage/' . $settings['business_logo']) }}" alt="Logo" class="h-16 w-auto">
                                    @else
                                        <div class="h-16 w-16 bg-gray-200 rounded flex items-center justify-center">
                                            <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                    @endif
                                    <div class="flex-1">
                                        <h4 class="text-xl font-bold text-gray-900" id="preview-name">{{ $settings['business_name'] }}</h4>
                                        <p class="text-sm text-gray-600 mt-1" id="preview-address">{{ $settings['business_address'] ?: 'Business address will appear here' }}</p>
                                        <div class="mt-2 space-y-1">
                                            <p class="text-sm text-gray-600" id="preview-phone">📞 {{ $settings['business_phone'] ?: 'Phone number' }}</p>
                                            <p class="text-sm text-gray-600" id="preview-email">✉️ {{ $settings['business_email'] ?: '<EMAIL>' }}</p>
                                            @if($settings['business_website'])
                                                <p class="text-sm text-gray-600" id="preview-website">🌐 {{ $settings['business_website'] }}</p>
                                            @endif
                                        </div>
                                        <div class="mt-2 text-xs text-gray-500">
                                            <span>GSTIN: {{ $settings['gstin'] ?: 'Not set' }}</span> | 
                                            <span>PAN: {{ $settings['pan_number'] ?: 'Not set' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('settings.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Save Business Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Live preview updates
            const fields = {
                'business_name': 'preview-name',
                'business_address': 'preview-address',
                'business_phone': 'preview-phone',
                'business_email': 'preview-email',
                'business_website': 'preview-website'
            };

            Object.keys(fields).forEach(fieldId => {
                const input = document.getElementById(fieldId);
                const preview = document.getElementById(fields[fieldId]);
                
                if (input && preview) {
                    input.addEventListener('input', function() {
                        let value = this.value;
                        
                        if (fieldId === 'business_phone') {
                            value = '📞 ' + (value || 'Phone number');
                        } else if (fieldId === 'business_email') {
                            value = '✉️ ' + (value || '<EMAIL>');
                        } else if (fieldId === 'business_website') {
                            value = '🌐 ' + value;
                            preview.style.display = value === '🌐 ' ? 'none' : 'block';
                        } else if (fieldId === 'business_address') {
                            value = value || 'Business address will appear here';
                        }
                        
                        preview.textContent = value;
                    });
                }
            });

            // Format GSTIN and PAN inputs
            const gstinInput = document.getElementById('gstin');
            const panInput = document.getElementById('pan_number');

            if (gstinInput) {
                gstinInput.addEventListener('input', function() {
                    this.value = this.value.toUpperCase();
                });
            }

            if (panInput) {
                panInput.addEventListener('input', function() {
                    this.value = this.value.toUpperCase();
                });
            }
        });
    </script>
</x-app-layout>
