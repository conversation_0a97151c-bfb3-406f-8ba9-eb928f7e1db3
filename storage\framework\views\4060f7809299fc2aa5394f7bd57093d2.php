<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Saving Schemes')); ?>

            </h2>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create_saving_scheme')): ?>
                <a href="<?php echo e(route('saving-schemes.create')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Create New Scheme
                </a>
            <?php endif; ?>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <?php if(session('success')): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="text-sm font-medium text-gray-500">Active Schemes</div>
                        <div class="text-2xl font-bold text-blue-600"><?php echo e($schemes->where('status', 'active')->count()); ?></div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="text-sm font-medium text-gray-500">Matured Schemes</div>
                        <div class="text-2xl font-bold text-green-600"><?php echo e($schemes->where('status', 'matured')->count()); ?></div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="text-sm font-medium text-gray-500">Overdue Schemes</div>
                        <div class="text-2xl font-bold text-red-600"><?php echo e($schemes->filter(fn($s) => $s->is_overdue)->count()); ?></div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="text-sm font-medium text-gray-500">Total Collections</div>
                        <div class="text-2xl font-bold text-gray-900">₹<?php echo e(number_format($schemes->sum('total_paid'), 2)); ?></div>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="<?php echo e(route('saving-schemes.index')); ?>" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                                   placeholder="Search by scheme #, name, customer..." 
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Status</option>
                                <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                                <option value="matured" <?php echo e(request('status') === 'matured' ? 'selected' : ''); ?>>Matured</option>
                                <option value="closed" <?php echo e(request('status') === 'closed' ? 'selected' : ''); ?>>Closed</option>
                                <option value="defaulted" <?php echo e(request('status') === 'defaulted' ? 'selected' : ''); ?>>Defaulted</option>
                            </select>
                        </div>
                        <div>
                            <select name="scheme_name" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Schemes</option>
                                <?php $__currentLoopData = $schemes->pluck('scheme_name')->unique(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schemeName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($schemeName); ?>" <?php echo e(request('scheme_name') === $schemeName ? 'selected' : ''); ?>><?php echo e($schemeName); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="overdue" value="1" <?php echo e(request('overdue') ? 'checked' : ''); ?>

                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Overdue Only</span>
                            </label>
                        </div>
                        <div class="flex space-x-2">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded flex-1">
                                Search
                            </button>
                            <a href="<?php echo e(route('saving-schemes.index')); ?>" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Schemes Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scheme Details</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount & Duration</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php $__empty_1 = true; $__currentLoopData = $schemes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $scheme): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="<?php echo e($scheme->is_overdue ? 'bg-red-50' : ''); ?>">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900"><?php echo e($scheme->scheme_number); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo e($scheme->scheme_name); ?></div>
                                            <div class="text-xs text-gray-400">Started: <?php echo e($scheme->start_date->format('d M, Y')); ?></div>
                                            <?php if($scheme->is_overdue): ?>
                                                <div class="text-xs text-red-600 font-semibold">Overdue</div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900"><?php echo e($scheme->customer->name); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo e($scheme->customer->mobile); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">₹<?php echo e(number_format($scheme->monthly_amount, 0)); ?>/month</div>
                                            <div class="text-sm text-gray-500"><?php echo e($scheme->duration_months); ?> months</div>
                                            <div class="text-xs text-gray-400">Maturity: <?php echo e($scheme->maturity_date->format('d M, Y')); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                ₹<?php echo e(number_format($scheme->total_paid, 0)); ?> / ₹<?php echo e(number_format($scheme->expected_total, 0)); ?>

                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                                <div class="bg-blue-600 h-2 rounded-full" style="width: <?php echo e($scheme->completion_percentage); ?>%"></div>
                                            </div>
                                            <div class="text-xs text-gray-500 mt-1"><?php echo e(number_format($scheme->completion_percentage, 1)); ?>% complete</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                <?php echo e($scheme->status === 'active' ? 'bg-blue-100 text-blue-800' : 
                                                   ($scheme->status === 'matured' ? 'bg-green-100 text-green-800' : 
                                                   ($scheme->status === 'closed' ? 'bg-gray-100 text-gray-800' : 'bg-red-100 text-red-800'))); ?>">
                                                <?php echo e(ucfirst($scheme->status)); ?>

                                            </span>
                                            <?php if($scheme->auto_debit): ?>
                                                <div class="text-xs text-blue-600 mt-1">Auto Debit</div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_saving_schemes')): ?>
                                                    <a href="<?php echo e(route('saving-schemes.show', $scheme)); ?>" class="text-indigo-600 hover:text-indigo-900">View</a>
                                                <?php endif; ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage_scheme_payments')): ?>
                                                    <?php if($scheme->status === 'active'): ?>
                                                        <a href="<?php echo e(route('saving-schemes.add-payment', $scheme)); ?>" class="text-green-600 hover:text-green-900">Add Payment</a>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit_saving_scheme')): ?>
                                                    <?php if($scheme->status === 'active'): ?>
                                                        <a href="<?php echo e(route('saving-schemes.edit', $scheme)); ?>" class="text-blue-600 hover:text-blue-900">Edit</a>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                                <?php if($scheme->status === 'matured'): ?>
                                                    <a href="<?php echo e(route('saving-schemes.certificate', $scheme)); ?>" class="text-purple-600 hover:text-purple-900">Certificate</a>
                                                <?php endif; ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete_saving_scheme')): ?>
                                                    <?php if($scheme->payments->count() === 0): ?>
                                                        <form method="POST" action="<?php echo e(route('saving-schemes.destroy', $scheme)); ?>" class="inline" 
                                                              onsubmit="return confirm('Are you sure you want to delete this scheme?')">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                                        </form>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            No saving schemes found.
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        <?php echo e($schemes->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\proj\jewel-pro\resources\views/saving-schemes/index.blade.php ENDPATH**/ ?>