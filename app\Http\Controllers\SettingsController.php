<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Temporarily disable permission check for testing
        // $this->middleware('permission:manage_settings');
    }

    /**
     * Settings dashboard
     */
    public function index()
    {
        $settings = $this->getAllSettings();

        return view('settings.index', compact('settings'));
    }

    /**
     * Business profile settings
     */
    public function businessProfile()
    {
        $settings = $this->getAllSettings();

        return view('settings.business-profile', compact('settings'));
    }

    /**
     * Update business profile
     */
    public function updateBusinessProfile(Request $request)
    {
        $validated = $request->validate([
            'business_name' => 'required|string|max:255',
            'business_address' => 'required|string',
            'business_phone' => 'required|string|max:20',
            'business_email' => 'required|email|max:255',
            'business_website' => 'nullable|url|max:255',
            'gstin' => 'required|string|max:15',
            'pan_number' => 'required|string|max:10',
            'business_logo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'license_number' => 'nullable|string|max:255',
            'established_year' => 'nullable|integer|min:1900|max:' . date('Y'),
        ]);

        // Handle logo upload
        if ($request->hasFile('business_logo')) {
            $logoPath = $request->file('business_logo')->store('logos', 'public');
            $validated['business_logo'] = $logoPath;
        }

        $this->updateSettings($validated);

        return redirect()->route('settings.business-profile')
            ->with('success', 'Business profile updated successfully.');
    }

    /**
     * Tax settings
     */
    public function taxSettings()
    {
        $settings = $this->getAllSettings();

        return view('settings.tax-settings', compact('settings'));
    }

    /**
     * Update tax settings
     */
    public function updateTaxSettings(Request $request)
    {
        $validated = $request->validate([
            'cgst_rate' => 'required|numeric|min:0|max:50',
            'sgst_rate' => 'required|numeric|min:0|max:50',
            'igst_rate' => 'required|numeric|min:0|max:50',
            'tax_inclusive_pricing' => 'boolean',
            'hsn_code_gold' => 'required|string|max:10',
            'hsn_code_silver' => 'required|string|max:10',
            'hsn_code_diamond' => 'required|string|max:10',
            'hsn_code_making_charges' => 'required|string|max:10',
        ]);

        $this->updateSettings($validated);

        return redirect()->route('settings.tax-settings')
            ->with('success', 'Tax settings updated successfully.');
    }

    /**
     * Print settings
     */
    public function printSettings()
    {
        $settings = $this->getAllSettings();

        return view('settings.print-settings', compact('settings'));
    }

    /**
     * Update print settings
     */
    public function updatePrintSettings(Request $request)
    {
        $validated = $request->validate([
            'invoice_template' => 'required|in:template1,template2,template3',
            'print_logo_on_invoice' => 'boolean',
            'print_terms_conditions' => 'boolean',
            'invoice_footer_text' => 'nullable|string',
            'receipt_paper_size' => 'required|in:A4,A5,thermal_80mm,thermal_58mm',
            'auto_print_invoice' => 'boolean',
            'print_barcode_on_invoice' => 'boolean',
            'invoice_copies' => 'required|integer|min:1|max:5',
        ]);

        $this->updateSettings($validated);

        return redirect()->route('settings.print-settings')
            ->with('success', 'Print settings updated successfully.');
    }

    /**
     * Barcode settings
     */
    public function barcodeSettings()
    {
        $settings = $this->getAllSettings();

        return view('settings.barcode-settings', compact('settings'));
    }

    /**
     * Update barcode settings
     */
    public function updateBarcodeSettings(Request $request)
    {
        $validated = $request->validate([
            'barcode_format' => 'required|in:CODE128,CODE39,EAN13,EAN8',
            'barcode_width' => 'required|integer|min:1|max:10',
            'barcode_height' => 'required|integer|min:20|max:200',
            'barcode_prefix' => 'nullable|string|max:5',
            'auto_generate_barcode' => 'boolean',
            'label_template' => 'required|in:small,medium,large,custom',
            'include_price_on_label' => 'boolean',
            'include_weight_on_label' => 'boolean',
            'include_huid_on_label' => 'boolean',
        ]);

        $this->updateSettings($validated);

        return redirect()->route('settings.barcode-settings')
            ->with('success', 'Barcode settings updated successfully.');
    }

    /**
     * System settings
     */
    public function systemSettings()
    {
        $settings = $this->getAllSettings();

        return view('settings.system-settings', compact('settings'));
    }

    /**
     * Update system settings
     */
    public function updateSystemSettings(Request $request)
    {
        $validated = $request->validate([
            'currency_symbol' => 'required|string|max:5',
            'currency_position' => 'required|in:before,after',
            'decimal_places' => 'required|integer|min:0|max:4',
            'date_format' => 'required|in:d/m/Y,m/d/Y,Y-m-d,d-m-Y',
            'time_format' => 'required|in:12,24',
            'timezone' => 'required|string',
            'language' => 'required|in:en,hi',
            'backup_frequency' => 'required|in:daily,weekly,monthly',
            'low_stock_threshold' => 'required|integer|min:1|max:100',
        ]);

        $this->updateSettings($validated);

        return redirect()->route('settings.system-settings')
            ->with('success', 'System settings updated successfully.');
    }

    /**
     * Get all settings
     */
    private function getAllSettings()
    {
        return Cache::remember('app_settings', 3600, function () {
            $defaultSettings = [
                // Business Profile
                'business_name' => 'Jewelry Pro',
                'business_address' => '',
                'business_phone' => '',
                'business_email' => '',
                'business_website' => '',
                'gstin' => '',
                'pan_number' => '',
                'business_logo' => null,
                'license_number' => '',
                'established_year' => date('Y'),

                // Tax Settings
                'cgst_rate' => 1.5,
                'sgst_rate' => 1.5,
                'igst_rate' => 3.0,
                'tax_inclusive_pricing' => false,
                'hsn_code_gold' => '71131900',
                'hsn_code_silver' => '71141100',
                'hsn_code_diamond' => '71023100',
                'hsn_code_making_charges' => '99954',

                // Print Settings
                'invoice_template' => 'template1',
                'print_logo_on_invoice' => true,
                'print_terms_conditions' => true,
                'invoice_footer_text' => 'Thank you for your business!',
                'receipt_paper_size' => 'A4',
                'auto_print_invoice' => false,
                'print_barcode_on_invoice' => true,
                'invoice_copies' => 1,

                // Barcode Settings
                'barcode_format' => 'CODE128',
                'barcode_width' => 2,
                'barcode_height' => 50,
                'barcode_prefix' => 'JP',
                'auto_generate_barcode' => true,
                'label_template' => 'medium',
                'include_price_on_label' => true,
                'include_weight_on_label' => true,
                'include_huid_on_label' => true,

                // System Settings
                'currency_symbol' => '₹',
                'currency_position' => 'before',
                'decimal_places' => 2,
                'date_format' => 'd/m/Y',
                'time_format' => '12',
                'timezone' => 'Asia/Kolkata',
                'language' => 'en',
                'backup_frequency' => 'weekly',
                'low_stock_threshold' => 5,
            ];

            // Load settings from storage or database
            $storedSettings = [];
            if (Storage::exists('settings.json')) {
                $storedSettings = json_decode(Storage::get('settings.json'), true) ?? [];
            }

            return array_merge($defaultSettings, $storedSettings);
        });
    }

    /**
     * Update settings
     */
    private function updateSettings(array $newSettings)
    {
        $currentSettings = $this->getAllSettings();
        $updatedSettings = array_merge($currentSettings, $newSettings);

        // Save to storage
        Storage::put('settings.json', json_encode($updatedSettings, JSON_PRETTY_PRINT));

        // Clear cache
        Cache::forget('app_settings');
    }
}
