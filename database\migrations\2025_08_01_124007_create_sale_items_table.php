<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sale_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sale_id')->constrained('sales')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products');
            $table->integer('quantity');
            $table->decimal('metal_rate', 10, 2);
            $table->decimal('making_charges', 10, 2);
            $table->decimal('stone_charges', 10, 2)->default(0);
            $table->decimal('wastage_amount', 10, 2)->default(0);
            $table->decimal('item_total', 10, 2);
            $table->decimal('cgst_rate', 5, 2)->default(1.5);
            $table->decimal('sgst_rate', 5, 2)->default(1.5);
            $table->decimal('igst_rate', 5, 2)->default(3.0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sale_items');
    }
};
