<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Repair Job') }}
            </h2>
            <a href="{{ route('repairs.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Repairs
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('repairs.store') }}" enctype="multipart/form-data">
                        @csrf

                        <!-- Customer Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                            <div class="grid grid-cols-1 gap-6">
                                <div>
                                    <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer *</label>
                                    <select name="customer_id" id="customer_id" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Customer</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}" {{ old('customer_id') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }} - {{ $customer->mobile }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('customer_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Item Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Item Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="item_name" class="block text-sm font-medium text-gray-700">Item Name *</label>
                                    <input type="text" name="item_name" id="item_name" value="{{ old('item_name') }}" required
                                           placeholder="e.g., Gold Ring, Silver Necklace"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('item_name')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="estimated_charges" class="block text-sm font-medium text-gray-700">Estimated Charges (₹) *</label>
                                    <input type="number" name="estimated_charges" id="estimated_charges" value="{{ old('estimated_charges') }}" 
                                           step="0.01" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('estimated_charges')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div class="mt-4">
                                <label for="issue_description" class="block text-sm font-medium text-gray-700">Issue Description *</label>
                                <textarea name="issue_description" id="issue_description" rows="4" required
                                          placeholder="Describe the issue with the item in detail..."
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('issue_description') }}</textarea>
                                @error('issue_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Date Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Date Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="received_date" class="block text-sm font-medium text-gray-700">Received Date *</label>
                                    <input type="date" name="received_date" id="received_date" value="{{ old('received_date', today()->format('Y-m-d')) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('received_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="promised_date" class="block text-sm font-medium text-gray-700">Promised Date *</label>
                                    <input type="date" name="promised_date" id="promised_date" value="{{ old('promised_date', today()->addDays(7)->format('Y-m-d')) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('promised_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Receipt Image -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Receipt Image</h3>
                            <div>
                                <label for="receipt_image" class="block text-sm font-medium text-gray-700">Upload Receipt Image</label>
                                <input type="file" name="receipt_image" id="receipt_image" accept="image/*"
                                       class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                <p class="mt-1 text-sm text-gray-500">Upload an image of the item for reference (JPEG, PNG, JPG - Max 2MB)</p>
                                @error('receipt_image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-8">
                            <label for="repair_notes" class="block text-sm font-medium text-gray-700">Repair Notes</label>
                            <textarea name="repair_notes" id="repair_notes" rows="3"
                                      placeholder="Any additional notes about the repair..."
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('repair_notes') }}</textarea>
                            @error('repair_notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('repairs.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create Repair Job
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const receivedDateInput = document.getElementById('received_date');
            const promisedDateInput = document.getElementById('promised_date');

            // Update promised date when received date changes
            receivedDateInput.addEventListener('change', function() {
                const receivedDate = new Date(this.value);
                const promisedDate = new Date(receivedDate);
                promisedDate.setDate(promisedDate.getDate() + 7); // Add 7 days by default
                
                promisedDateInput.value = promisedDate.toISOString().split('T')[0];
            });

            // Validate that promised date is not before received date
            promisedDateInput.addEventListener('change', function() {
                const receivedDate = new Date(receivedDateInput.value);
                const promisedDate = new Date(this.value);
                
                if (promisedDate < receivedDate) {
                    alert('Promised date cannot be before received date');
                    this.value = receivedDateInput.value;
                }
            });
        });
    </script>
</x-app-layout>
