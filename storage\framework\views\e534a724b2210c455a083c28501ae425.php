<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e(config('app.name', 'Laravel')); ?></title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Font Awesome for Icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

        <!-- Custom Styles -->
        <style>
            [x-cloak] { display: none !important; }

            /* Sidebar animations */
            .sidebar-enter {
                transform: translateX(-100%);
            }
            .sidebar-enter-active {
                transform: translateX(0);
                transition: transform 0.3s ease-in-out;
            }

            /* Scrollbar styling for sidebar */
            .sidebar-scroll {
                scrollbar-width: thin;
                scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
            }
            .sidebar-scroll::-webkit-scrollbar {
                width: 6px;
            }
            .sidebar-scroll::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 3px;
            }
            .sidebar-scroll::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 3px;
            }
            .sidebar-scroll::-webkit-scrollbar-thumb:hover {
                background: rgba(255, 255, 255, 0.5);
            }

            /* Ensure sidebar takes full height */
            .sidebar-container {
                height: 100vh;
                max-height: 100vh;
            }

            /* Gradient background for cards */
            .gradient-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            /* Hover effects */
            .hover-lift {
                transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            }
            .hover-lift:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            }
        </style>
    </head>
    <body class="font-sans antialiased bg-gray-50">
        <div class="min-h-screen flex">
            <!-- Sidebar -->
            <?php echo $__env->make('layouts.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <!-- Main Content Area -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <!-- Top Navigation -->
                <?php echo $__env->make('layouts.topbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                <!-- Page Content -->
                <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
                    <!-- Page Heading -->
                    <?php if(isset($header)): ?>
                        <div class="bg-white shadow-sm border-b border-gray-200">
                            <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                                <?php echo e($header); ?>

                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Page Content -->
                    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
                        <?php echo e($slot); ?>

                    </div>
                </main>
            </div>
        </div>

        <!-- Mobile menu overlay -->
        <div x-data="{ sidebarOpen: false }"
             x-show="sidebarOpen"
             x-cloak
             @click="sidebarOpen = false"
             class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden">
        </div>

        <script>
            // Initialize Alpine.js data for sidebar
            document.addEventListener('alpine:init', () => {
                Alpine.data('sidebar', () => ({
                    sidebarOpen: false,
                    toggleSidebar() {
                        this.sidebarOpen = !this.sidebarOpen;
                    }
                }));
            });
        </script>
    </body>
</html>
<?php /**PATH C:\proj\jewel-pro\resources\views/layouts/app.blade.php ENDPATH**/ ?>