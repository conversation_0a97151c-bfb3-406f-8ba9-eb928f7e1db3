<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Repair;
use App\Models\Customer;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

class RepairController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Temporarily disable permission checks for testing
        // $this->middleware('permission:view_repairs')->only(['index', 'show']);
        // $this->middleware('permission:create_repair')->only(['create', 'store']);
        // $this->middleware('permission:edit_repair')->only(['edit', 'update']);
        // $this->middleware('permission:delete_repair')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Repair::with(['customer', 'createdBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('repair_number', 'like', "%{$search}%")
                  ->orWhere('item_name', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('mobile', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by overdue
        if ($request->filled('overdue') && $request->overdue === '1') {
            $query->overdue();
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            if ($request->payment_status === 'paid') {
                $query->where('payment_received', true);
            } elseif ($request->payment_status === 'pending') {
                $query->where('payment_received', false);
            }
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->whereDate('received_date', '>=', $request->from_date);
        }
        if ($request->filled('to_date')) {
            $query->whereDate('received_date', '<=', $request->to_date);
        }

        $repairs = $query->latest('received_date')->paginate(15);

        return view('repairs.index', compact('repairs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = Customer::active()->orderBy('name')->get();

        return view('repairs.create', compact('customers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'item_name' => 'required|string|max:255',
            'issue_description' => 'required|string',
            'estimated_charges' => 'required|numeric|min:0',
            'received_date' => 'required|date',
            'promised_date' => 'required|date|after_or_equal:received_date',
            'repair_notes' => 'nullable|string',
            'receipt_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        // Handle receipt image upload
        if ($request->hasFile('receipt_image')) {
            $validated['receipt_image_path'] = $request->file('receipt_image')
                ->store('repair-receipts', 'public');
        }

        $validated['created_by'] = auth()->id();
        $validated['status'] = 'received';

        $repair = Repair::create($validated);

        return redirect()->route('repairs.index')
            ->with('success', 'Repair job created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Repair $repair)
    {
        $repair->load(['customer', 'createdBy']);

        return view('repairs.show', compact('repair'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Repair $repair)
    {
        $customers = Customer::active()->orderBy('name')->get();

        return view('repairs.edit', compact('repair', 'customers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Repair $repair)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'item_name' => 'required|string|max:255',
            'issue_description' => 'required|string',
            'estimated_charges' => 'required|numeric|min:0',
            'actual_charges' => 'nullable|numeric|min:0',
            'received_date' => 'required|date',
            'promised_date' => 'required|date|after_or_equal:received_date',
            'completed_date' => 'nullable|date|after_or_equal:received_date',
            'delivered_date' => 'nullable|date|after_or_equal:received_date',
            'status' => 'required|in:received,in_progress,completed,delivered',
            'repair_notes' => 'nullable|string',
            'payment_received' => 'boolean',
            'receipt_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        // Handle receipt image upload
        if ($request->hasFile('receipt_image')) {
            // Delete old image if exists
            if ($repair->receipt_image_path) {
                Storage::disk('public')->delete($repair->receipt_image_path);
            }

            $validated['receipt_image_path'] = $request->file('receipt_image')
                ->store('repair-receipts', 'public');
        }

        // Auto-set dates based on status
        if ($validated['status'] === 'completed' && !$validated['completed_date']) {
            $validated['completed_date'] = today();
        }

        if ($validated['status'] === 'delivered' && !$validated['delivered_date']) {
            $validated['delivered_date'] = today();
            if (!$validated['completed_date']) {
                $validated['completed_date'] = today();
            }
        }

        $repair->update($validated);

        return redirect()->route('repairs.index')
            ->with('success', 'Repair job updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Repair $repair)
    {
        // Delete receipt image if exists
        if ($repair->receipt_image_path) {
            Storage::disk('public')->delete($repair->receipt_image_path);
        }

        $repair->delete();

        return redirect()->route('repairs.index')
            ->with('success', 'Repair job deleted successfully.');
    }

    /**
     * Update repair status
     */
    public function updateStatus(Request $request, Repair $repair)
    {
        $validated = $request->validate([
            'status' => 'required|in:received,in_progress,completed,delivered',
            'actual_charges' => 'nullable|numeric|min:0',
            'repair_notes' => 'nullable|string',
        ]);

        $updateData = [
            'status' => $validated['status'],
            'repair_notes' => $validated['repair_notes'],
        ];

        if (isset($validated['actual_charges'])) {
            $updateData['actual_charges'] = $validated['actual_charges'];
        }

        // Auto-set dates based on status
        if ($validated['status'] === 'completed' && !$repair->completed_date) {
            $updateData['completed_date'] = today();
        }

        if ($validated['status'] === 'delivered') {
            $updateData['delivered_date'] = today();
            if (!$repair->completed_date) {
                $updateData['completed_date'] = today();
            }
        }

        $repair->update($updateData);

        return redirect()->route('repairs.show', $repair)
            ->with('success', 'Repair status updated successfully.');
    }

    /**
     * Generate repair receipt PDF
     */
    public function receipt(Repair $repair)
    {
        $repair->load(['customer', 'createdBy']);

        $pdf = Pdf::loadView('repairs.receipt', compact('repair'));

        return $pdf->download('repair-receipt-' . $repair->repair_number . '.pdf');
    }

    /**
     * Generate delivery slip PDF
     */
    public function deliverySlip(Repair $repair)
    {
        $repair->load(['customer', 'createdBy']);

        $pdf = Pdf::loadView('repairs.delivery-slip', compact('repair'));

        return $pdf->download('delivery-slip-' . $repair->repair_number . '.pdf');
    }
}
