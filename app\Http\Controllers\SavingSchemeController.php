<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SavingScheme;
use App\Models\SchemePayment;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;

class SavingSchemeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Temporarily disable permission checks for testing
        // $this->middleware('permission:view_saving_schemes')->only(['index', 'show']);
        // $this->middleware('permission:create_saving_scheme')->only(['create', 'store']);
        // $this->middleware('permission:edit_saving_scheme')->only(['edit', 'update']);
        // $this->middleware('permission:delete_saving_scheme')->only(['destroy']);
        // $this->middleware('permission:manage_scheme_payments')->only(['addPayment', 'storePayment']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = SavingScheme::with(['customer', 'payments', 'createdBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('scheme_number', 'like', "%{$search}%")
                  ->orWhere('scheme_name', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('mobile', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by scheme name
        if ($request->filled('scheme_name')) {
            $query->where('scheme_name', $request->scheme_name);
        }

        // Filter by overdue
        if ($request->filled('overdue') && $request->overdue === '1') {
            $query->overdue();
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->whereDate('start_date', '>=', $request->from_date);
        }
        if ($request->filled('to_date')) {
            $query->whereDate('start_date', '<=', $request->to_date);
        }

        $schemes = $query->latest('start_date')->paginate(15);

        return view('saving-schemes.index', compact('schemes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = Customer::active()->orderBy('name')->get();

        return view('saving-schemes.create', compact('customers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'scheme_name' => 'required|string|max:255',
            'monthly_amount' => 'required|numeric|min:100',
            'duration_months' => 'required|integer|min:6|max:60',
            'start_date' => 'required|date|after_or_equal:today',
            'bonus_amount' => 'nullable|numeric|min:0',
            'auto_debit' => 'boolean',
            'notes' => 'nullable|string',
        ]);

        // Calculate maturity date
        $startDate = \Carbon\Carbon::parse($validated['start_date']);
        $maturityDate = $startDate->copy()->addMonths($validated['duration_months']);

        // Calculate total value (expected total + bonus)
        $expectedTotal = $validated['monthly_amount'] * $validated['duration_months'];
        $bonusAmount = $validated['bonus_amount'] ?? 0;
        $totalValue = $expectedTotal + $bonusAmount;

        $validated['maturity_date'] = $maturityDate;
        $validated['total_value'] = $totalValue;
        $validated['created_by'] = auth()->id();

        $scheme = SavingScheme::create($validated);

        return redirect()->route('saving-schemes.index')
            ->with('success', 'Saving scheme created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(SavingScheme $savingScheme)
    {
        $savingScheme->load(['customer', 'payments.createdBy', 'createdBy']);

        // Get payment schedule
        $paymentSchedule = $this->generatePaymentSchedule($savingScheme);

        return view('saving-schemes.show', compact('savingScheme', 'paymentSchedule'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SavingScheme $savingScheme)
    {
        if ($savingScheme->status !== 'active') {
            return redirect()->route('saving-schemes.show', $savingScheme)
                ->with('error', 'Cannot edit non-active scheme.');
        }

        $customers = Customer::active()->orderBy('name')->get();

        return view('saving-schemes.edit', compact('savingScheme', 'customers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SavingScheme $savingScheme)
    {
        if ($savingScheme->status !== 'active') {
            return redirect()->route('saving-schemes.show', $savingScheme)
                ->with('error', 'Cannot update non-active scheme.');
        }

        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'scheme_name' => 'required|string|max:255',
            'monthly_amount' => 'required|numeric|min:100',
            'duration_months' => 'required|integer|min:6|max:60',
            'start_date' => 'required|date',
            'bonus_amount' => 'nullable|numeric|min:0',
            'auto_debit' => 'boolean',
            'notes' => 'nullable|string',
            'status' => 'required|in:active,matured,closed,defaulted',
        ]);

        // Recalculate maturity date and total value
        $startDate = \Carbon\Carbon::parse($validated['start_date']);
        $maturityDate = $startDate->copy()->addMonths($validated['duration_months']);
        $expectedTotal = $validated['monthly_amount'] * $validated['duration_months'];
        $bonusAmount = $validated['bonus_amount'] ?? 0;
        $totalValue = $expectedTotal + $bonusAmount;

        $validated['maturity_date'] = $maturityDate;
        $validated['total_value'] = $totalValue;

        $savingScheme->update($validated);

        return redirect()->route('saving-schemes.index')
            ->with('success', 'Saving scheme updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SavingScheme $savingScheme)
    {
        if ($savingScheme->payments()->count() > 0) {
            return redirect()->route('saving-schemes.index')
                ->with('error', 'Cannot delete scheme with existing payments.');
        }

        $savingScheme->delete();

        return redirect()->route('saving-schemes.index')
            ->with('success', 'Saving scheme deleted successfully.');
    }

    /**
     * Show payment form
     */
    public function addPayment(SavingScheme $savingScheme)
    {
        if ($savingScheme->status !== 'active') {
            return redirect()->route('saving-schemes.show', $savingScheme)
                ->with('error', 'Cannot add payment to non-active scheme.');
        }

        return view('saving-schemes.add-payment', compact('savingScheme'));
    }

    /**
     * Store payment
     */
    public function storePayment(Request $request, SavingScheme $savingScheme)
    {
        if ($savingScheme->status !== 'active') {
            return redirect()->route('saving-schemes.show', $savingScheme)
                ->with('error', 'Cannot add payment to non-active scheme.');
        }

        $validated = $request->validate([
            'amount' => 'required|numeric|min:1',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,card,upi,bank_transfer',
            'transaction_reference' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        DB::transaction(function () use ($validated, $savingScheme) {
            // Determine if payment is late
            $nextDueDate = $savingScheme->next_due_date;
            $paymentDate = \Carbon\Carbon::parse($validated['payment_date']);
            $isLate = $nextDueDate && $paymentDate->gt($nextDueDate);

            // Calculate late fee if applicable
            $lateFee = 0;
            if ($isLate && $nextDueDate) {
                $daysLate = $nextDueDate->diffInDays($paymentDate);
                $lateFee = min(500, $daysLate * 10); // ₹10 per day, max ₹500
            }

            // Create payment record
            SchemePayment::create([
                'saving_scheme_id' => $savingScheme->id,
                'amount' => $validated['amount'],
                'payment_date' => $validated['payment_date'],
                'due_date' => $nextDueDate,
                'payment_method' => $validated['payment_method'],
                'transaction_reference' => $validated['transaction_reference'],
                'is_late' => $isLate,
                'late_fee' => $lateFee,
                'notes' => $validated['notes'],
                'created_by' => auth()->id(),
            ]);

            // Update scheme totals
            $savingScheme->increment('total_paid', $validated['amount'] + $lateFee);

            // Check if scheme is completed
            if ($savingScheme->total_paid >= $savingScheme->expected_total) {
                $savingScheme->update(['status' => 'matured']);
            }
        });

        return redirect()->route('saving-schemes.show', $savingScheme)
            ->with('success', 'Payment added successfully.');
    }

    /**
     * Generate payment schedule
     */
    private function generatePaymentSchedule(SavingScheme $scheme)
    {
        $schedule = [];
        $currentDate = $scheme->start_date->copy();
        $payments = $scheme->payments->keyBy(function ($payment) {
            return $payment->payment_date->format('Y-m');
        });

        for ($month = 1; $month <= $scheme->duration_months; $month++) {
            $monthKey = $currentDate->format('Y-m');
            $payment = $payments->get($monthKey);

            $schedule[] = [
                'month' => $month,
                'due_date' => $currentDate->copy(),
                'expected_amount' => $scheme->monthly_amount,
                'paid_amount' => $payment ? $payment->amount : 0,
                'payment_date' => $payment ? $payment->payment_date : null,
                'status' => $this->getPaymentStatus($currentDate, $payment, $scheme->monthly_amount),
                'is_late' => $payment ? $payment->is_late : false,
                'late_fee' => $payment ? $payment->late_fee : 0,
            ];

            $currentDate->addMonth();
        }

        return $schedule;
    }

    /**
     * Get payment status for schedule
     */
    private function getPaymentStatus($dueDate, $payment, $expectedAmount)
    {
        if ($payment) {
            return $payment->amount >= $expectedAmount ? 'paid' : 'partial';
        }

        if ($dueDate->isFuture()) {
            return 'upcoming';
        }

        return 'overdue';
    }

    /**
     * Generate scheme certificate
     */
    public function certificate(SavingScheme $savingScheme)
    {
        if ($savingScheme->status !== 'matured') {
            return redirect()->route('saving-schemes.show', $savingScheme)
                ->with('error', 'Certificate can only be generated for matured schemes.');
        }

        $savingScheme->load(['customer', 'payments']);

        $pdf = Pdf::loadView('saving-schemes.certificate', compact('savingScheme'));

        return $pdf->download('scheme-certificate-' . $savingScheme->scheme_number . '.pdf');
    }

    /**
     * Get scheme summary for dashboard
     */
    public function getSchemeSummary()
    {
        $summary = [
            'active_schemes' => SavingScheme::active()->count(),
            'matured_schemes' => SavingScheme::matured()->count(),
            'overdue_schemes' => SavingScheme::overdue()->count(),
            'total_collections' => SavingScheme::sum('total_paid'),
            'pending_collections' => SavingScheme::active()->get()->sum('pending_amount'),
        ];

        return response()->json($summary);
    }
}
