<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repairs', function (Blueprint $table) {
            $table->id();
            $table->string('repair_number')->unique();
            $table->foreignId('customer_id')->constrained('customers');
            $table->string('item_name');
            $table->text('issue_description');
            $table->decimal('estimated_charges', 10, 2)->default(0);
            $table->decimal('actual_charges', 10, 2)->default(0);
            $table->date('received_date');
            $table->date('promised_date');
            $table->date('completed_date')->nullable();
            $table->date('delivered_date')->nullable();
            $table->enum('status', ['received', 'in_progress', 'completed', 'delivered'])->default('received');
            $table->text('repair_notes')->nullable();
            $table->string('receipt_image_path')->nullable();
            $table->boolean('payment_received')->default(false);
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repairs');
    }
};
