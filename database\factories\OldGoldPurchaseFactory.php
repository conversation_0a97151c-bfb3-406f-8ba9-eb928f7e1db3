<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Customer;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OldGoldPurchase>
 */
class OldGoldPurchaseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $metalTypes = ['Gold', 'Silver'];
        $metalType = $this->faker->randomElement($metalTypes);

        $purityOptions = [
            'Gold' => ['22K', '18K', '14K'],
            'Silver' => ['925', '999']
        ];
        $purity = $this->faker->randomElement($purityOptions[$metalType]);

        $grossWeight = $this->faker->randomFloat(3, 5, 100);
        $stoneWeight = $this->faker->boolean(30) ? $this->faker->randomFloat(3, 0.1, $grossWeight * 0.2) : 0;
        $netWeight = $grossWeight - $stoneWeight;

        $meltingLossPercentage = $this->faker->randomFloat(2, 0, 5);
        $meltingLossWeight = ($netWeight * $meltingLossPercentage) / 100;
        $finalWeight = $netWeight - $meltingLossWeight;

        // Rate based on metal type
        $ratePerGram = $metalType === 'Gold' ?
            $this->faker->randomFloat(2, 5500, 6500) :
            $this->faker->randomFloat(2, 75, 95);

        $totalAmount = $finalWeight * $ratePerGram;

        // Payment method distribution
        $paymentMethod = $this->faker->randomElement(['cash', 'voucher', 'mixed']);

        $cashPaid = 0;
        $voucherAmount = 0;
        $status = 'purchased';

        if ($paymentMethod === 'cash') {
            $cashPaid = $totalAmount;
        } elseif ($paymentMethod === 'voucher') {
            $voucherAmount = $totalAmount;
            $status = 'converted_to_voucher';
        } else { // mixed
            $cashPaid = $totalAmount * 0.6;
            $voucherAmount = $totalAmount * 0.4;
            $status = 'converted_to_voucher';
        }

        return [
            'customer_id' => Customer::inRandomOrder()->first()?->id ?? Customer::factory(),
            'metal_type' => $metalType,
            'purity' => $purity,
            'gross_weight' => $grossWeight,
            'stone_weight' => $stoneWeight,
            'net_weight' => $netWeight,
            'melting_loss_percentage' => $meltingLossPercentage,
            'melting_loss_weight' => $meltingLossWeight,
            'final_weight' => $finalWeight,
            'rate_per_gram' => $ratePerGram,
            'total_amount' => $totalAmount,
            'voucher_amount' => $voucherAmount,
            'cash_paid' => $cashPaid,
            'status' => $status,
            'notes' => $this->faker->optional(0.3)->sentence(),
            'created_by' => User::where('email', '<EMAIL>')->first()?->id ?? 1,
        ];
    }
}
