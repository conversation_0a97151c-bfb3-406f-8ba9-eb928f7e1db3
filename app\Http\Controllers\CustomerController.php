<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use Illuminate\Support\Facades\Storage;

class CustomerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Temporarily disable permission checks for testing
        // $this->middleware('permission:view_customers')->only(['index', 'show']);
        // $this->middleware('permission:create_customer')->only(['create', 'store']);
        // $this->middleware('permission:edit_customer')->only(['edit', 'update']);
        // $this->middleware('permission:delete_customer')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Customer::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('mobile', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $customers = $query->latest()->paginate(15);

        return view('customers.index', compact('customers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('customers.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'mobile' => 'required|string|max:15|unique:customers,mobile',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'pincode' => 'nullable|string|max:10',
            'date_of_birth' => 'nullable|date',
            'anniversary_date' => 'nullable|date',
            'kyc_type' => 'nullable|string|max:255',
            'kyc_number' => 'nullable|string|max:255',
            'kyc_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'notes' => 'nullable|string',
        ]);

        // Handle KYC document upload
        if ($request->hasFile('kyc_document')) {
            $validated['kyc_document_path'] = $request->file('kyc_document')
                ->store('kyc-documents', 'public');
        }

        $customer = Customer::create($validated);

        return redirect()->route('customers.index')
            ->with('success', 'Customer created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Customer $customer)
    {
        $customer->load(['sales', 'estimates', 'repairs', 'oldGoldPurchases', 'savingSchemes']);

        return view('customers.show', compact('customer'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Customer $customer)
    {
        return view('customers.edit', compact('customer'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Customer $customer)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'mobile' => 'required|string|max:15|unique:customers,mobile,' . $customer->id,
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:255',
            'state' => 'nullable|string|max:255',
            'pincode' => 'nullable|string|max:10',
            'date_of_birth' => 'nullable|date',
            'anniversary_date' => 'nullable|date',
            'kyc_type' => 'nullable|string|max:255',
            'kyc_number' => 'nullable|string|max:255',
            'kyc_document' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        // Handle KYC document upload
        if ($request->hasFile('kyc_document')) {
            // Delete old document if exists
            if ($customer->kyc_document_path) {
                Storage::disk('public')->delete($customer->kyc_document_path);
            }

            $validated['kyc_document_path'] = $request->file('kyc_document')
                ->store('kyc-documents', 'public');
        }

        $customer->update($validated);

        return redirect()->route('customers.index')
            ->with('success', 'Customer updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Customer $customer)
    {
        // Check if customer has any related records
        if ($customer->sales()->count() > 0 ||
            $customer->estimates()->count() > 0 ||
            $customer->repairs()->count() > 0) {
            return redirect()->route('customers.index')
                ->with('error', 'Cannot delete customer with existing transactions.');
        }

        // Delete KYC document if exists
        if ($customer->kyc_document_path) {
            Storage::disk('public')->delete($customer->kyc_document_path);
        }

        $customer->delete();

        return redirect()->route('customers.index')
            ->with('success', 'Customer deleted successfully.');
    }
}
