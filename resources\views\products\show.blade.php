<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Product Details') }} - {{ $product->name }}
            </h2>
            <div class="flex space-x-2">
                @can('edit_product')
                    <a href="{{ route('products.edit', $product) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit Product
                    </a>
                @endcan
                @can('manage_barcode')
                    <a href="{{ route('products.barcode', $product) }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Print Barcode
                    </a>
                @endcan
                <a href="{{ route('products.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Inventory
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Product Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Product Image -->
                        <div class="lg:col-span-1">
                            @if($product->image_path)
                                <img src="{{ asset('storage/' . $product->image_path) }}" alt="{{ $product->name }}" 
                                     class="w-full h-64 object-cover rounded-lg shadow-md">
                            @else
                                <div class="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <span class="text-gray-500">No Image</span>
                                </div>
                            @endif
                            
                            <!-- Status Badge -->
                            <div class="mt-4">
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                    {{ $product->status === 'in_stock' ? 'bg-green-100 text-green-800' : 
                                       ($product->status === 'sold' ? 'bg-red-100 text-red-800' : 
                                       ($product->status === 'reserved' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800')) }}">
                                    {{ ucfirst(str_replace('_', ' ', $product->status)) }}
                                </span>
                            </div>
                        </div>

                        <!-- Product Details -->
                        <div class="lg:col-span-2">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Basic Info -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Product Name</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->name }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Category</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->category }}</dd>
                                        </div>
                                        @if($product->subcategory)
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Subcategory</dt>
                                                <dd class="text-sm text-gray-900">{{ $product->subcategory }}</dd>
                                            </div>
                                        @endif
                                        @if($product->size)
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Size</dt>
                                                <dd class="text-sm text-gray-900">{{ $product->size }}</dd>
                                            </div>
                                        @endif
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Quantity</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->quantity }}</dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Metal Info -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Metal Information</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Metal Type</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->metal_type }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Purity</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->purity }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Gross Weight</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->gross_weight }} grams</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Net Weight</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->net_weight }} grams</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Wastage</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->wastage_percentage }}% ({{ number_format($product->wastage_amount, 3) }}g)</dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Stone Info -->
                                @if($product->has_stones)
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 mb-4">Stone Information</h3>
                                        <dl class="space-y-2">
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Stone Weight</dt>
                                                <dd class="text-sm text-gray-900">{{ $product->stone_weight }} carats</dd>
                                            </div>
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Stone Charges</dt>
                                                <dd class="text-sm text-gray-900">₹{{ number_format($product->stone_charges, 2) }}</dd>
                                            </div>
                                        </dl>
                                    </div>
                                @endif

                                <!-- Pricing Info -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing Information</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Making Charges</dt>
                                            <dd class="text-sm text-gray-900">₹{{ number_format($product->making_charges, 2) }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Cost Price</dt>
                                            <dd class="text-sm text-gray-900">₹{{ number_format($product->cost_price, 2) }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Selling Price</dt>
                                            <dd class="text-sm text-gray-900 font-semibold">₹{{ number_format($product->selling_price, 2) }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Profit</dt>
                                            <dd class="text-sm {{ $product->profit >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                                ₹{{ number_format($product->profit, 2) }} ({{ number_format($product->profit_margin, 2) }}%)
                                            </dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Identification -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Identification</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Barcode</dt>
                                            <dd class="text-sm text-gray-900 font-mono">{{ $product->barcode }}</dd>
                                        </div>
                                        @if($product->huid_number)
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">HUID Number</dt>
                                                <dd class="text-sm text-gray-900 font-mono">{{ $product->huid_number }}</dd>
                                            </div>
                                        @endif
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">HSN Code</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->hsn_code }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Added On</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->created_at->format('d M, Y h:i A') }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->updated_at->format('d M, Y h:i A') }}</dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>

                            @if($product->description)
                                <div class="mt-6">
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">Description</h3>
                                    <p class="text-sm text-gray-700">{{ $product->description }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales History -->
            @if($product->saleItems->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Sales History</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($product->saleItems->take(10) as $saleItem)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ $saleItem->sale->invoice_number }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $saleItem->sale->customer->name }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $saleItem->sale->sale_date->format('d M, Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $saleItem->quantity }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ₹{{ number_format($saleItem->item_total, 2) }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
