<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('metal_rates', function (Blueprint $table) {
            $table->id();
            $table->string('metal_type'); // Gold, Silver, Platinum
            $table->string('purity'); // 22K, 18K, 14K, 925 Silver, etc.
            $table->decimal('rate_per_gram', 10, 2);
            $table->decimal('rate_per_10_gram', 10, 2);
            $table->date('effective_date');
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();

            $table->unique(['metal_type', 'purity', 'effective_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('metal_rates');
    }
};
