<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('saving_schemes', function (Blueprint $table) {
            $table->id();
            $table->string('scheme_number')->unique();
            $table->foreignId('customer_id')->constrained('customers');
            $table->string('scheme_name'); // Gold Scheme, Silver Scheme
            $table->decimal('monthly_amount', 10, 2);
            $table->integer('duration_months');
            $table->date('start_date');
            $table->date('maturity_date');
            $table->decimal('total_paid', 10, 2)->default(0);
            $table->decimal('bonus_amount', 10, 2)->default(0);
            $table->decimal('total_value', 10, 2)->default(0);
            $table->enum('status', ['active', 'matured', 'closed', 'defaulted'])->default('active');
            $table->boolean('auto_debit')->default(false);
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('saving_schemes');
    }
};
