<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\MetalRate;
use Illuminate\Support\Facades\Storage;
use Milon\Barcode\DNS1D;

class ProductController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Temporarily disable permission checks for testing
        // $this->middleware('permission:view_inventory')->only(['index', 'show']);
        // $this->middleware('permission:create_product')->only(['create', 'store']);
        // $this->middleware('permission:edit_product')->only(['edit', 'update']);
        // $this->middleware('permission:delete_product')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%")
                  ->orWhere('huid_number', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by metal type
        if ($request->filled('metal_type')) {
            $query->where('metal_type', $request->metal_type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $products = $query->latest()->paginate(15);

        // Get filter options
        $categories = Product::distinct()->pluck('category')->filter();
        $metalTypes = Product::distinct()->pluck('metal_type')->filter();

        return view('products.index', compact('products', 'categories', 'metalTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get current metal rates for pricing calculation
        $metalRates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->get()
            ->groupBy(['metal_type', 'purity'])
            ->map(function ($group) {
                return $group->first();
            });

        return view('products.create', compact('metalRates'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|string|max:255',
            'subcategory' => 'nullable|string|max:255',
            'metal_type' => 'required|string|max:255',
            'purity' => 'required|string|max:255',
            'gross_weight' => 'required|numeric|min:0',
            'net_weight' => 'required|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'wastage_percentage' => 'nullable|numeric|min:0|max:100',
            'making_charges' => 'required|numeric|min:0',
            'stone_charges' => 'nullable|numeric|min:0',
            'hsn_code' => 'nullable|string|max:10',
            'huid_number' => 'nullable|string|max:255|unique:products,huid_number',
            'barcode' => 'nullable|string|max:255|unique:products,barcode',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:1',
            'size' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'has_stones' => 'boolean',
            'stone_details' => 'nullable|array',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image_path'] = $request->file('image')
                ->store('product-images', 'public');
        }

        // Set default values
        $validated['stone_weight'] = $validated['stone_weight'] ?? 0;
        $validated['stone_charges'] = $validated['stone_charges'] ?? 0;
        $validated['wastage_percentage'] = $validated['wastage_percentage'] ?? 0;
        $validated['hsn_code'] = $validated['hsn_code'] ?? '71131900';
        $validated['has_stones'] = $request->has('has_stones');

        $product = Product::create($validated);

        return redirect()->route('products.index')
            ->with('success', 'Product created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        return view('products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        // Get current metal rates for pricing calculation
        $metalRates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->get()
            ->groupBy(['metal_type', 'purity'])
            ->map(function ($group) {
                return $group->first();
            });

        return view('products.edit', compact('product', 'metalRates'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|string|max:255',
            'subcategory' => 'nullable|string|max:255',
            'metal_type' => 'required|string|max:255',
            'purity' => 'required|string|max:255',
            'gross_weight' => 'required|numeric|min:0',
            'net_weight' => 'required|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'wastage_percentage' => 'nullable|numeric|min:0|max:100',
            'making_charges' => 'required|numeric|min:0',
            'stone_charges' => 'nullable|numeric|min:0',
            'hsn_code' => 'nullable|string|max:10',
            'huid_number' => 'nullable|string|max:255|unique:products,huid_number,' . $product->id,
            'barcode' => 'nullable|string|max:255|unique:products,barcode,' . $product->id,
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:0',
            'size' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'has_stones' => 'boolean',
            'stone_details' => 'nullable|array',
            'status' => 'required|in:in_stock,sold,reserved,repair',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($product->image_path) {
                Storage::disk('public')->delete($product->image_path);
            }

            $validated['image_path'] = $request->file('image')
                ->store('product-images', 'public');
        }

        // Set default values
        $validated['stone_weight'] = $validated['stone_weight'] ?? 0;
        $validated['stone_charges'] = $validated['stone_charges'] ?? 0;
        $validated['wastage_percentage'] = $validated['wastage_percentage'] ?? 0;
        $validated['hsn_code'] = $validated['hsn_code'] ?? '71131900';
        $validated['has_stones'] = $request->has('has_stones');

        $product->update($validated);

        return redirect()->route('products.index')
            ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        // Check if product has any related sales
        if ($product->saleItems()->count() > 0) {
            return redirect()->route('products.index')
                ->with('error', 'Cannot delete product with existing sales.');
        }

        // Delete product image if exists
        if ($product->image_path) {
            Storage::disk('public')->delete($product->image_path);
        }

        $product->delete();

        return redirect()->route('products.index')
            ->with('success', 'Product deleted successfully.');
    }

    /**
     * Generate and display barcode for a product
     */
    public function barcode(Product $product)
    {
        $barcode = new DNS1D();
        $barcodeImage = $barcode->getBarcodePNG($product->barcode, 'C128', 3, 33, array(1, 1, 1), true);

        return view('products.barcode', compact('product', 'barcodeImage'));
    }
}
