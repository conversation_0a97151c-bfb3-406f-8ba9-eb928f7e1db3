<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Repair Tracking') }}
            </h2>
            @can('create_repair')
                <a href="{{ route('repairs.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    New Repair Job
                </a>
            @endcan
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Search and Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('repairs.index') }}" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Search by repair #, item, customer..." 
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Status</option>
                                <option value="received" {{ request('status') === 'received' ? 'selected' : '' }}>Received</option>
                                <option value="in_progress" {{ request('status') === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="delivered" {{ request('status') === 'delivered' ? 'selected' : '' }}>Delivered</option>
                            </select>
                        </div>
                        <div>
                            <select name="payment_status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Payments</option>
                                <option value="paid" {{ request('payment_status') === 'paid' ? 'selected' : '' }}>Paid</option>
                                <option value="pending" {{ request('payment_status') === 'pending' ? 'selected' : '' }}>Pending</option>
                            </select>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="overdue" value="1" {{ request('overdue') ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Overdue Only</span>
                            </label>
                        </div>
                        <div>
                            <input type="date" name="from_date" value="{{ request('from_date') }}" 
                                   placeholder="From Date"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div class="flex space-x-2">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded flex-1">
                                Search
                            </button>
                            <a href="{{ route('repairs.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Repairs Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Repair #</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item & Issue</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dates</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Charges</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($repairs as $repair)
                                    <tr class="{{ $repair->is_overdue ? 'bg-red-50' : '' }}">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $repair->repair_number }}</div>
                                            <div class="text-sm text-gray-500">{{ $repair->received_date->format('d M, Y') }}</div>
                                            @if($repair->is_overdue)
                                                <div class="text-xs text-red-600 font-semibold">{{ $repair->days_overdue }} days overdue</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $repair->customer->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $repair->customer->mobile }}</div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $repair->item_name }}</div>
                                            <div class="text-sm text-gray-500">{{ Str::limit($repair->issue_description, 50) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                <div>Promised: {{ $repair->promised_date->format('d M, Y') }}</div>
                                                @if($repair->completed_date)
                                                    <div class="text-green-600">Completed: {{ $repair->completed_date->format('d M, Y') }}</div>
                                                @endif
                                                @if($repair->delivered_date)
                                                    <div class="text-blue-600">Delivered: {{ $repair->delivered_date->format('d M, Y') }}</div>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">
                                                <div>Est: ₹{{ number_format($repair->estimated_charges, 2) }}</div>
                                                @if($repair->actual_charges > 0)
                                                    <div class="font-medium">Act: ₹{{ number_format($repair->actual_charges, 2) }}</div>
                                                @endif
                                                <div class="text-xs {{ $repair->payment_received ? 'text-green-600' : 'text-red-600' }}">
                                                    {{ $repair->payment_received ? 'Paid' : 'Pending' }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $repair->status_color }}">
                                                {{ ucfirst(str_replace('_', ' ', $repair->status)) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                @can('view_repairs')
                                                    <a href="{{ route('repairs.show', $repair) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                                @endcan
                                                @can('edit_repair')
                                                    <a href="{{ route('repairs.edit', $repair) }}" class="text-blue-600 hover:text-blue-900">Edit</a>
                                                @endcan
                                                <a href="{{ route('repairs.receipt', $repair) }}" class="text-green-600 hover:text-green-900">Receipt</a>
                                                @if($repair->status === 'completed')
                                                    <a href="{{ route('repairs.delivery-slip', $repair) }}" class="text-purple-600 hover:text-purple-900">Delivery</a>
                                                @endif
                                                @can('delete_repair')
                                                    <form method="POST" action="{{ route('repairs.destroy', $repair) }}" class="inline" 
                                                          onsubmit="return confirm('Are you sure you want to delete this repair job?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                                    </form>
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            No repair jobs found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $repairs->links() }}
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            @if($repairs->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mt-6">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Total Repairs</div>
                            <div class="text-2xl font-bold text-gray-900">{{ $repairs->total() }}</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">In Progress</div>
                            <div class="text-2xl font-bold text-yellow-600">{{ $repairs->where('status', 'in_progress')->count() }}</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Completed</div>
                            <div class="text-2xl font-bold text-green-600">{{ $repairs->where('status', 'completed')->count() }}</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Overdue</div>
                            <div class="text-2xl font-bold text-red-600">{{ $repairs->filter(fn($r) => $r->is_overdue)->count() }}</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Total Value</div>
                            <div class="text-2xl font-bold text-gray-900">₹{{ number_format($repairs->sum('estimated_charges'), 2) }}</div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
