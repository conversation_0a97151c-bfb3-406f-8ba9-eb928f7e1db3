<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Customer;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Repair>
 */
class RepairFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $itemTypes = [
            'Gold Ring', 'Silver Ring', 'Gold Necklace', 'Silver Necklace',
            'Gold Earrings', 'Silver Earrings', 'Gold Bracelet', 'Silver Bracelet',
            'Gold Chain', 'Silver Chain', 'Gold Pendant', 'Silver Pendant'
        ];

        $issues = [
            'Chain link broken, needs repair',
            'Stone missing, needs replacement',
            'Clasp not working properly',
            'Ring size adjustment needed',
            'Polish and cleaning required',
            'Prong setting loose, stone security issue',
            'Earring back missing replacement needed',
            'Bracelet extension required',
            'Pendant loop damaged',
            'Surface scratches need buffing',
            'Tarnish removal and restoration',
            'Soldering joint repair needed'
        ];

        $receivedDate = $this->faker->dateTimeBetween('-60 days', 'now');
        $promisedDate = $this->faker->dateTimeBetween($receivedDate, '+30 days');

        $status = $this->faker->randomElement(['received', 'in_progress', 'completed', 'delivered']);

        $completedDate = null;
        $deliveredDate = null;

        if (in_array($status, ['completed', 'delivered'])) {
            $completedDate = $this->faker->dateTimeBetween($receivedDate, $promisedDate);
        }

        if ($status === 'delivered') {
            $deliveredDate = $this->faker->dateTimeBetween($completedDate ?: $receivedDate, 'now');
        }

        $estimatedCharges = $this->faker->randomFloat(2, 100, 2000);
        $actualCharges = in_array($status, ['completed', 'delivered']) ?
            $this->faker->randomFloat(2, $estimatedCharges * 0.8, $estimatedCharges * 1.3) : 0;

        return [
            'customer_id' => Customer::inRandomOrder()->first()?->id ?? Customer::factory(),
            'item_name' => $this->faker->randomElement($itemTypes),
            'issue_description' => $this->faker->randomElement($issues),
            'estimated_charges' => $estimatedCharges,
            'actual_charges' => $actualCharges,
            'received_date' => $receivedDate,
            'promised_date' => $promisedDate,
            'completed_date' => $completedDate,
            'delivered_date' => $deliveredDate,
            'status' => $status,
            'repair_notes' => $this->faker->optional(0.6)->sentence(),
            'payment_received' => $status === 'delivered' ? true : $this->faker->boolean(30),
            'created_by' => User::where('email', '<EMAIL>')->first()?->id ?? 1,
        ];
    }
}
