<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'mobile' => $this->faker->unique()->numerify('##########'),
            'email' => $this->faker->optional(0.7)->safeEmail(),
            'address' => $this->faker->optional(0.8)->address(),
            'city' => $this->faker->optional(0.8)->city(),
            'state' => $this->faker->optional(0.8)->state(),
            'pincode' => $this->faker->optional(0.8)->postcode(),
            'date_of_birth' => $this->faker->optional(0.6)->dateTimeBetween('-70 years', '-18 years'),
            'anniversary_date' => $this->faker->optional(0.4)->dateTimeBetween('-30 years', 'now'),
            'kyc_type' => $this->faker->optional(0.5)->randomElement(['Aadhar', 'PAN', 'Passport', 'Driving License', 'Voter ID']),
            'kyc_number' => $this->faker->optional(0.5)->bothify('??##########'),
            'total_purchases' => $this->faker->randomFloat(2, 0, 500000),
            'total_orders' => $this->faker->numberBetween(0, 50),
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
            'notes' => $this->faker->optional(0.3)->sentence(),
        ];
    }
}
