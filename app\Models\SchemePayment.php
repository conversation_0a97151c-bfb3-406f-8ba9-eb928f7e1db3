<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SchemePayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'saving_scheme_id',
        'amount',
        'payment_date',
        'due_date',
        'payment_method',
        'transaction_reference',
        'is_late',
        'late_fee',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'late_fee' => 'decimal:2',
        'payment_date' => 'date',
        'due_date' => 'date',
        'is_late' => 'boolean',
    ];

    // Relationships
    public function savingScheme()
    {
        return $this->belongsTo(SavingScheme::class, 'saving_scheme_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Accessors
    public function getTotalAmountAttribute()
    {
        return $this->amount + $this->late_fee;
    }

    public function getDaysLateAttribute()
    {
        if (!$this->is_late || !$this->due_date) return 0;
        return $this->due_date->diffInDays($this->payment_date);
    }

    // Scopes
    public function scopeLate($query)
    {
        return $query->where('is_late', true);
    }

    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('payment_date', now()->month)
                    ->whereYear('payment_date', now()->year);
    }
}
