<nav x-data="{ open: false }" class="bg-white border-b border-gray-100 shadow-sm relative z-50">
    <!-- Metal Rates Ticker -->
    <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 border-b border-yellow-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between py-2 text-sm">
                <div class="flex items-center space-x-6 overflow-x-auto">
                    <div class="flex items-center space-x-1 text-yellow-800 font-medium whitespace-nowrap">
                        <i class="fas fa-coins text-yellow-600"></i>
                        <span>Live Rates:</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-1 text-gray-700 whitespace-nowrap">
                            <span class="font-medium">Gold 22K:</span>
                            <span class="text-green-600 font-semibold">₹6,200/g</span>
                        </div>
                        <div class="flex items-center space-x-1 text-gray-700 whitespace-nowrap">
                            <span class="font-medium">Gold 18K:</span>
                            <span class="text-green-600 font-semibold">₹5,100/g</span>
                        </div>
                        <div class="flex items-center space-x-1 text-gray-700 whitespace-nowrap">
                            <span class="font-medium">Silver 925:</span>
                            <span class="text-green-600 font-semibold">₹85/g</span>
                        </div>
                    </div>
                </div>
                <div class="text-xs text-gray-500 whitespace-nowrap">
                    Updated: {{ now()->format('M d, H:i') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Primary Navigation Menu -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex">
                <!-- Logo -->
                <div class="shrink-0 flex items-center">
                    <a href="{{ route('dashboard') }}">
                        <x-application-logo class="block h-9 w-auto fill-current text-gray-800" />
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden space-x-8 sm:-my-px sm:ms-10 sm:flex">
                    <x-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
                        {{ __('Dashboard') }}
                    </x-nav-link>
                    <x-nav-link :href="route('customers.index')" :active="request()->routeIs('customers.*')">
                        {{ __('Customers') }}
                    </x-nav-link>
                    <x-nav-link :href="route('products.index')" :active="request()->routeIs('products.*')">
                        {{ __('Inventory') }}
                    </x-nav-link>
                    <x-nav-link :href="route('sales.index')" :active="request()->routeIs('sales.*')">
                        {{ __('Sales') }}
                    </x-nav-link>
                    <x-nav-link :href="route('old-gold-purchases.index')" :active="request()->routeIs('old-gold-purchases.*')">
                        {{ __('Old Gold') }}
                    </x-nav-link>
                    <x-nav-link :href="route('estimates.index')" :active="request()->routeIs('estimates.*')">
                        {{ __('Estimates') }}
                    </x-nav-link>
                    <x-nav-link :href="route('repairs.index')" :active="request()->routeIs('repairs.*')">
                        {{ __('Repairs') }}
                    </x-nav-link>
                    <x-nav-link :href="route('barcodes.index')" :active="request()->routeIs('barcodes.*')">
                        {{ __('Barcodes') }}
                    </x-nav-link>
                    <x-nav-link :href="route('metal-rates.index')" :active="request()->routeIs('metal-rates.*')">
                        {{ __('Metal Rates') }}
                    </x-nav-link>
                    <x-nav-link :href="route('calculator')" :active="request()->routeIs('calculator*')">
                        {{ __('Calculator') }}
                    </x-nav-link>
                    <x-nav-link :href="route('saving-schemes.index')" :active="request()->routeIs('saving-schemes.*')">
                        {{ __('Saving Schemes') }}
                    </x-nav-link>
                    <x-nav-link :href="route('reports.index')" :active="request()->routeIs('reports.*')">
                        {{ __('Reports') }}
                    </x-nav-link>
                    <x-nav-link :href="route('settings.index')" :active="request()->routeIs('settings.*')">
                        {{ __('Settings') }}
                    </x-nav-link>
                </div>
            </div>

            <!-- Settings Dropdown -->
            <div class="hidden sm:flex sm:items-center sm:ms-6">
                <x-dropdown align="right" width="48">
                    <x-slot name="trigger">
                        <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 bg-white hover:text-gray-700 focus:outline-none transition ease-in-out duration-150">
                            <div>{{ Auth::user()->name }}</div>

                            <div class="ms-1">
                                <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </button>
                    </x-slot>

                    <x-slot name="content">
                        <x-dropdown-link :href="route('profile.edit')">
                            {{ __('Profile') }}
                        </x-dropdown-link>

                        <!-- Authentication -->
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf

                            <x-dropdown-link :href="route('logout')"
                                    onclick="event.preventDefault();
                                                this.closest('form').submit();">
                                {{ __('Log Out') }}
                            </x-dropdown-link>
                        </form>
                    </x-slot>
                </x-dropdown>
            </div>

            <!-- Hamburger -->
            <div class="-me-2 flex items-center sm:hidden">
                <button @click="open = ! open" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 focus:text-gray-500 transition duration-150 ease-in-out">
                    <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                        <path :class="{'hidden': open, 'inline-flex': ! open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        <path :class="{'hidden': ! open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Responsive Navigation Menu -->
    <div :class="{'block': open, 'hidden': ! open}" class="hidden sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
            <x-responsive-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')">
                {{ __('Dashboard') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('customers.index')" :active="request()->routeIs('customers.*')">
                {{ __('Customers') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('products.index')" :active="request()->routeIs('products.*')">
                {{ __('Inventory') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('sales.index')" :active="request()->routeIs('sales.*')">
                {{ __('Sales') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('old-gold-purchases.index')" :active="request()->routeIs('old-gold-purchases.*')">
                {{ __('Old Gold') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('estimates.index')" :active="request()->routeIs('estimates.*')">
                {{ __('Estimates') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('repairs.index')" :active="request()->routeIs('repairs.*')">
                {{ __('Repairs') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('barcodes.index')" :active="request()->routeIs('barcodes.*')">
                {{ __('Barcodes') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('metal-rates.index')" :active="request()->routeIs('metal-rates.*')">
                {{ __('Metal Rates') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('calculator')" :active="request()->routeIs('calculator*')">
                {{ __('Calculator') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('saving-schemes.index')" :active="request()->routeIs('saving-schemes.*')">
                {{ __('Saving Schemes') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('reports.index')" :active="request()->routeIs('reports.*')">
                {{ __('Reports') }}
            </x-responsive-nav-link>
            <x-responsive-nav-link :href="route('settings.index')" :active="request()->routeIs('settings.*')">
                {{ __('Settings') }}
            </x-responsive-nav-link>
        </div>

        <!-- Responsive Settings Options -->
        <div class="pt-4 pb-1 border-t border-gray-200">
            <div class="px-4">
                <div class="font-medium text-base text-gray-800">{{ Auth::user()->name }}</div>
                <div class="font-medium text-sm text-gray-500">{{ Auth::user()->email }}</div>
            </div>

            <div class="mt-3 space-y-1">
                <x-responsive-nav-link :href="route('profile.edit')">
                    {{ __('Profile') }}
                </x-responsive-nav-link>

                <!-- Authentication -->
                <form method="POST" action="{{ route('logout') }}">
                    @csrf

                    <x-responsive-nav-link :href="route('logout')"
                            onclick="event.preventDefault();
                                        this.closest('form').submit();">
                        {{ __('Log Out') }}
                    </x-responsive-nav-link>
                </form>
            </div>
        </div>
    </div>
</nav>
