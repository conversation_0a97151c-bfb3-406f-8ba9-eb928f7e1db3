<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MetalRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'metal_type',
        'purity',
        'rate_per_gram',
        'rate_per_10_gram',
        'effective_date',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'rate_per_gram' => 'decimal:2',
        'rate_per_10_gram' => 'decimal:2',
        'effective_date' => 'date',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCurrent($query)
    {
        return $query->where('effective_date', '<=', today())
                    ->orderBy('effective_date', 'desc');
    }
}
