<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Barcode Labels</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 10px;
            font-size: 10px;
        }
        
        .labels-container {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .label {
            border: 1px solid #000;
            padding: 5px;
            text-align: center;
            page-break-inside: avoid;
            display: inline-block;
            vertical-align: top;
        }
        
        /* Small labels - 2x1 inch */
        .label-small {
            width: 180px;
            height: 90px;
            font-size: 8px;
        }
        
        /* Medium labels - 3x2 inch */
        .label-medium {
            width: 270px;
            height: 180px;
            font-size: 10px;
        }
        
        /* Large labels - 4x3 inch */
        .label-large {
            width: 360px;
            height: 270px;
            font-size: 12px;
        }
        
        .product-name {
            font-weight: bold;
            margin-bottom: 3px;
            word-wrap: break-word;
            line-height: 1.2;
        }
        
        .barcode-image {
            margin: 3px 0;
        }
        
        .barcode-text {
            font-family: 'Courier New', monospace;
            font-size: 8px;
            margin-bottom: 3px;
        }
        
        .product-details {
            margin-top: 3px;
            line-height: 1.1;
        }
        
        .price {
            font-weight: bold;
            font-size: 1.2em;
            color: #000;
        }
        
        .weight, .huid {
            font-size: 0.9em;
            color: #333;
        }
        
        .metal-info {
            font-size: 0.8em;
            color: #666;
            margin-bottom: 2px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 5px;
            }
            
            .label {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="labels-container">
        @foreach($barcodeData as $data)
            @for($i = 0; $i < $data['count']; $i++)
                <div class="label label-{{ $labelSize }}">
                    <!-- Product Name -->
                    <div class="product-name">
                        {{ Str::limit($data['product']->name, $labelSize === 'small' ? 20 : ($labelSize === 'medium' ? 30 : 40)) }}
                    </div>
                    
                    <!-- Metal Information -->
                    <div class="metal-info">
                        {{ $data['product']->metal_type }} {{ $data['product']->purity }}
                    </div>
                    
                    <!-- Barcode Image -->
                    <div class="barcode-image">
                        <img src="{{ $data['barcode'] }}" alt="Barcode" 
                             style="height: {{ $labelSize === 'small' ? '20px' : ($labelSize === 'medium' ? '30px' : '40px') }}; max-width: 100%;">
                    </div>
                    
                    <!-- Barcode Text -->
                    <div class="barcode-text">
                        {{ $data['product']->barcode }}
                    </div>
                    
                    <!-- Product Details -->
                    <div class="product-details">
                        @if($includePrice)
                            <div class="price">₹{{ number_format($data['product']->selling_price, 0) }}</div>
                        @endif
                        
                        @if($includeWeight)
                            <div class="weight">{{ $data['product']->net_weight }}g</div>
                        @endif
                        
                        @if($includeHuid && $data['product']->huid_number)
                            <div class="huid">HUID: {{ $data['product']->huid_number }}</div>
                        @endif
                    </div>
                </div>
            @endfor
        @endforeach
    </div>
</body>
</html>
