<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('estimate_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('estimate_id')->constrained('estimates')->onDelete('cascade');
            $table->string('item_name');
            $table->string('metal_type');
            $table->string('purity');
            $table->decimal('gross_weight', 8, 3);
            $table->decimal('net_weight', 8, 3);
            $table->decimal('stone_weight', 8, 3)->default(0);
            $table->decimal('metal_rate', 10, 2);
            $table->decimal('making_charges', 10, 2);
            $table->decimal('stone_charges', 10, 2)->default(0);
            $table->decimal('wastage_percentage', 5, 2)->default(0);
            $table->decimal('wastage_amount', 10, 2)->default(0);
            $table->decimal('item_total', 10, 2);
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('estimate_items');
    }
};
