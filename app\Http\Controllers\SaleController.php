<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Customer;
use App\Models\Product;
use App\Models\MetalRate;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;

class SaleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Temporarily disable permission checks for testing
        // $this->middleware('permission:view_sales')->only(['index', 'show']);
        // $this->middleware('permission:create_sale')->only(['create', 'store']);
        // $this->middleware('permission:edit_sale')->only(['edit', 'update']);
        // $this->middleware('permission:delete_sale')->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Sale::with(['customer', 'saleItems']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('customer', function ($customerQuery) use ($search) {
                      $customerQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('mobile', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->whereDate('sale_date', '>=', $request->from_date);
        }
        if ($request->filled('to_date')) {
            $query->whereDate('sale_date', '<=', $request->to_date);
        }

        // Filter by payment status
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        $sales = $query->latest('sale_date')->paginate(15);

        return view('sales.index', compact('sales'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = Customer::active()->orderBy('name')->get();
        $products = Product::inStock()->with('saleItems')->get();

        // Get current metal rates
        $metalRates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->get()
            ->groupBy(['metal_type', 'purity'])
            ->map(function ($group) {
                return $group->first();
            });

        return view('sales.create', compact('customers', 'products', 'metalRates'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'sale_date' => 'required|date',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.metal_rate' => 'required|numeric|min:0',
            'items.*.making_charges' => 'required|numeric|min:0',
            'items.*.stone_charges' => 'nullable|numeric|min:0',
            'items.*.wastage_amount' => 'nullable|numeric|min:0',
            'items.*.item_total' => 'required|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'cash_payment' => 'nullable|numeric|min:0',
            'card_payment' => 'nullable|numeric|min:0',
            'upi_payment' => 'nullable|numeric|min:0',
            'old_gold_adjustment' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        DB::transaction(function () use ($validated, $request) {
            // Calculate totals
            $subtotal = 0;
            $totalTax = 0;

            foreach ($validated['items'] as $item) {
                $subtotal += $item['item_total'];
                // Calculate tax (assuming 3% GST for jewelry)
                $itemTax = ($item['item_total'] * 3) / 100;
                $totalTax += $itemTax;
            }

            $discountAmount = $validated['discount_amount'] ?? 0;
            $totalAmount = $subtotal + $totalTax - $discountAmount;

            $totalPayment = ($validated['cash_payment'] ?? 0) +
                           ($validated['card_payment'] ?? 0) +
                           ($validated['upi_payment'] ?? 0) +
                           ($validated['old_gold_adjustment'] ?? 0);

            $balanceAmount = $totalAmount - $totalPayment;
            $paymentStatus = $balanceAmount <= 0 ? 'paid' : ($totalPayment > 0 ? 'partial' : 'pending');

            // Create sale
            $sale = Sale::create([
                'customer_id' => $validated['customer_id'],
                'sale_date' => $validated['sale_date'],
                'subtotal' => $subtotal,
                'cgst_amount' => $totalTax / 2, // Split GST into CGST and SGST
                'sgst_amount' => $totalTax / 2,
                'total_tax' => $totalTax,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'cash_payment' => $validated['cash_payment'] ?? 0,
                'card_payment' => $validated['card_payment'] ?? 0,
                'upi_payment' => $validated['upi_payment'] ?? 0,
                'old_gold_adjustment' => $validated['old_gold_adjustment'] ?? 0,
                'balance_amount' => $balanceAmount,
                'payment_status' => $paymentStatus,
                'notes' => $validated['notes'],
                'created_by' => auth()->id(),
            ]);

            // Create sale items and update product quantities
            foreach ($validated['items'] as $item) {
                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'metal_rate' => $item['metal_rate'],
                    'making_charges' => $item['making_charges'],
                    'stone_charges' => $item['stone_charges'] ?? 0,
                    'wastage_amount' => $item['wastage_amount'] ?? 0,
                    'item_total' => $item['item_total'],
                    'cgst_rate' => 1.5,
                    'sgst_rate' => 1.5,
                ]);

                // Update product quantity and status
                $product = Product::find($item['product_id']);
                $product->quantity -= $item['quantity'];
                if ($product->quantity <= 0) {
                    $product->status = 'sold';
                }
                $product->save();
            }

            // Update customer totals
            $customer = Customer::find($validated['customer_id']);
            $customer->total_purchases += $totalAmount;
            $customer->total_orders += 1;
            $customer->save();
        });

        return redirect()->route('sales.index')
            ->with('success', 'Sale created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Sale $sale)
    {
        $sale->load(['customer', 'saleItems.product', 'createdBy']);

        return view('sales.show', compact('sale'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Sale $sale)
    {
        $customers = Customer::active()->orderBy('name')->get();
        $products = Product::orderBy('name')->get();

        // Get current metal rates
        $metalRates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->get()
            ->groupBy(['metal_type', 'purity'])
            ->map(function ($group) {
                return $group->first();
            });

        $sale->load(['saleItems.product']);

        return view('sales.edit', compact('sale', 'customers', 'products', 'metalRates'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Sale $sale)
    {
        $validated = $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'sale_date' => 'required|date',
            'discount_amount' => 'nullable|numeric|min:0',
            'cash_payment' => 'nullable|numeric|min:0',
            'card_payment' => 'nullable|numeric|min:0',
            'upi_payment' => 'nullable|numeric|min:0',
            'old_gold_adjustment' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'payment_status' => 'required|in:paid,partial,pending',
        ]);

        $totalPayment = ($validated['cash_payment'] ?? 0) +
                       ($validated['card_payment'] ?? 0) +
                       ($validated['upi_payment'] ?? 0) +
                       ($validated['old_gold_adjustment'] ?? 0);

        $balanceAmount = $sale->total_amount - $totalPayment;

        $sale->update([
            'customer_id' => $validated['customer_id'],
            'sale_date' => $validated['sale_date'],
            'discount_amount' => $validated['discount_amount'] ?? 0,
            'cash_payment' => $validated['cash_payment'] ?? 0,
            'card_payment' => $validated['card_payment'] ?? 0,
            'upi_payment' => $validated['upi_payment'] ?? 0,
            'old_gold_adjustment' => $validated['old_gold_adjustment'] ?? 0,
            'balance_amount' => $balanceAmount,
            'payment_status' => $validated['payment_status'],
            'notes' => $validated['notes'],
        ]);

        return redirect()->route('sales.index')
            ->with('success', 'Sale updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Sale $sale)
    {
        DB::transaction(function () use ($sale) {
            // Restore product quantities
            foreach ($sale->saleItems as $saleItem) {
                $product = $saleItem->product;
                $product->quantity += $saleItem->quantity;
                $product->status = 'in_stock';
                $product->save();
            }

            // Update customer totals
            $customer = $sale->customer;
            $customer->total_purchases -= $sale->total_amount;
            $customer->total_orders -= 1;
            $customer->save();

            // Delete sale items and sale
            $sale->saleItems()->delete();
            $sale->delete();
        });

        return redirect()->route('sales.index')
            ->with('success', 'Sale deleted successfully.');
    }

    /**
     * Generate PDF invoice
     */
    public function invoice(Sale $sale)
    {
        $sale->load(['customer', 'saleItems.product', 'createdBy']);

        $pdf = Pdf::loadView('sales.invoice', compact('sale'));

        return $pdf->download('invoice-' . $sale->invoice_number . '.pdf');
    }

    /**
     * Print invoice
     */
    public function print(Sale $sale)
    {
        $sale->load(['customer', 'saleItems.product', 'createdBy']);

        return view('sales.print', compact('sale'));
    }
}
