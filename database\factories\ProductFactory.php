<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = ['Ring', 'Necklace', 'Earrings', 'Bracelet', 'Chain', 'Pendant', 'Bangles', 'Anklet'];
        $metalTypes = ['Gold', 'Silver', 'Platinum'];
        $category = $this->faker->randomElement($categories);
        $metalType = $this->faker->randomElement($metalTypes);

        // Purity based on metal type
        $purityOptions = [
            'Gold' => ['22K', '18K', '14K'],
            'Silver' => ['925', '999'],
            'Platinum' => ['950', '900']
        ];
        $purity = $this->faker->randomElement($purityOptions[$metalType]);

        $grossWeight = $this->faker->randomFloat(3, 1, 50);
        $netWeight = $grossWeight * $this->faker->randomFloat(3, 0.85, 0.95);
        $wastagePercentage = $this->faker->randomFloat(2, 5, 15);
        $makingCharges = $this->faker->randomFloat(2, 500, 5000);
        $hasStones = $this->faker->boolean(30); // 30% chance of having stones

        $costPrice = $this->faker->randomFloat(2, 5000, 50000);
        $sellingPrice = $costPrice * $this->faker->randomFloat(2, 1.1, 1.4); // 10-40% markup

        return [
            'name' => $category . ' - ' . $this->faker->words(2, true),
            'category' => $category,
            'subcategory' => $this->faker->optional(0.7)->word(),
            'metal_type' => $metalType,
            'purity' => $purity,
            'gross_weight' => $grossWeight,
            'net_weight' => $netWeight,
            'stone_weight' => $hasStones ? $this->faker->randomFloat(3, 0.1, 5) : 0,
            'wastage_percentage' => $wastagePercentage,
            'making_charges' => $makingCharges,
            'stone_charges' => $hasStones ? $this->faker->randomFloat(2, 100, 2000) : 0,
            'hsn_code' => '71131900',
            'huid_number' => $this->faker->optional(0.6)->bothify('################'),
            'cost_price' => $costPrice,
            'selling_price' => $sellingPrice,
            'quantity' => $this->faker->numberBetween(1, 10),
            'size' => $this->faker->optional(0.5)->randomElement(['XS', 'S', 'M', 'L', 'XL', '14', '16', '18', '20']),
            'description' => $this->faker->optional(0.7)->sentence(),
            'has_stones' => $hasStones,
            'stone_details' => $hasStones ? [
                'type' => $this->faker->randomElement(['Diamond', 'Ruby', 'Emerald', 'Sapphire', 'Pearl']),
                'clarity' => $this->faker->randomElement(['VS1', 'VS2', 'SI1', 'SI2']),
                'color' => $this->faker->randomElement(['D', 'E', 'F', 'G', 'H'])
            ] : null,
            'status' => $this->faker->randomElement(['in_stock', 'in_stock', 'in_stock', 'sold', 'reserved']), // Weighted towards in_stock
        ];
    }
}
