<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Repair extends Model
{
    use HasFactory;

    protected $fillable = [
        'repair_number',
        'customer_id',
        'item_name',
        'issue_description',
        'estimated_charges',
        'actual_charges',
        'received_date',
        'promised_date',
        'completed_date',
        'delivered_date',
        'status',
        'repair_notes',
        'receipt_image_path',
        'payment_received',
        'created_by',
    ];

    protected $casts = [
        'estimated_charges' => 'decimal:2',
        'actual_charges' => 'decimal:2',
        'received_date' => 'date',
        'promised_date' => 'date',
        'completed_date' => 'date',
        'delivered_date' => 'date',
        'payment_received' => 'boolean',
    ];

    // Relationships
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Accessors
    public function getIsOverdueAttribute()
    {
        return $this->promised_date < today() && !in_array($this->status, ['completed', 'delivered']);
    }

    public function getIsCompletedAttribute()
    {
        return $this->status === 'completed';
    }

    public function getIsDeliveredAttribute()
    {
        return $this->status === 'delivered';
    }

    public function getDaysInRepairAttribute()
    {
        $endDate = $this->delivered_date ?? today();
        return $this->received_date->diffInDays($endDate);
    }

    public function getDaysOverdueAttribute()
    {
        if (!$this->is_overdue) {
            return 0;
        }
        return $this->promised_date->diffInDays(today());
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'received' => 'bg-blue-100 text-blue-800',
            'in_progress' => 'bg-yellow-100 text-yellow-800',
            'completed' => 'bg-green-100 text-green-800',
            'delivered' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    // Scopes
    public function scopeReceived($query)
    {
        return $query->where('status', 'received');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    public function scopeOverdue($query)
    {
        return $query->where('promised_date', '<', today())
                    ->whereNotIn('status', ['completed', 'delivered']);
    }

    public function scopePending($query)
    {
        return $query->whereNotIn('status', ['delivered']);
    }

    // Boot method to generate repair number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($repair) {
            if (empty($repair->repair_number)) {
                $repair->repair_number = 'REP-' . date('Y') . '-' . str_pad(static::whereYear('created_at', date('Y'))->count() + 1, 6, '0', STR_PAD_LEFT);
            }
        });
    }
}
