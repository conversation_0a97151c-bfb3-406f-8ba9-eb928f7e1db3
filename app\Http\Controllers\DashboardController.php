<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Sale;
use App\Models\Estimate;
use App\Models\Repair;
use App\Models\MetalRate;

class DashboardController extends Controller
{
    public function index()
    {
        // Get dashboard statistics
        $stats = [
            'total_customers' => Customer::count(),
            'total_products' => Product::where('status', 'in_stock')->count(),
            'today_sales' => Sale::whereDate('sale_date', today())->sum('total_amount'),
            'pending_estimates' => Estimate::where('status', 'pending')->count(),
            'pending_repairs' => Repair::where('status', '!=', 'delivered')->count(),
        ];

        // Get recent sales
        $recent_sales = Sale::with('customer')
            ->latest()
            ->take(5)
            ->get();

        // Get current metal rates (simplified to avoid collection issues)
        $metal_rates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->limit(5)
            ->get();

        return view('dashboard', compact('stats', 'recent_sales', 'metal_rates'));
    }
}
