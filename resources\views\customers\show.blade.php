<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Customer Details') }} - {{ $customer->name }}
            </h2>
            <div class="flex space-x-2">
                @can('edit_customer')
                    <a href="{{ route('customers.edit', $customer) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit Customer
                    </a>
                @endcan
                <a href="{{ route('customers.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Customers
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Customer Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Basic Info -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Mobile</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->mobile }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->email ?: '-' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Date of Birth</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->date_of_birth?->format('d M, Y') ?: '-' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Anniversary</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->anniversary_date?->format('d M, Y') ?: '-' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="text-sm">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $customer->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $customer->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </dd>
                                </div>
                            </dl>
                        </div>

                        <!-- Address Info -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Address Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Address</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->address ?: '-' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">City</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->city ?: '-' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">State</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->state ?: '-' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Pincode</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->pincode ?: '-' }}</dd>
                                </div>
                            </dl>
                        </div>

                        <!-- KYC & Stats -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">KYC & Statistics</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">KYC Type</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->kyc_type ?: '-' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">KYC Number</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->kyc_number ?: '-' }}</dd>
                                </div>
                                @if($customer->kyc_document_path)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">KYC Document</dt>
                                        <dd class="text-sm">
                                            <a href="{{ asset('storage/' . $customer->kyc_document_path) }}" target="_blank" class="text-blue-600 hover:text-blue-800">View Document</a>
                                        </dd>
                                    </div>
                                @endif
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Total Purchases</dt>
                                    <dd class="text-sm text-gray-900">₹{{ number_format($customer->total_purchases, 2) }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Total Orders</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->total_orders }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Member Since</dt>
                                    <dd class="text-sm text-gray-900">{{ $customer->created_at->format('d M, Y') }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    @if($customer->notes)
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Notes</h3>
                            <p class="text-sm text-gray-700">{{ $customer->notes }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Transaction History Tabs -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm active" data-tab="sales">
                            Sales ({{ $customer->sales->count() }})
                        </button>
                        <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="estimates">
                            Estimates ({{ $customer->estimates->count() }})
                        </button>
                        <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="repairs">
                            Repairs ({{ $customer->repairs->count() }})
                        </button>
                        <button class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="schemes">
                            Schemes ({{ $customer->savingSchemes->count() }})
                        </button>
                    </nav>
                </div>

                <!-- Sales Tab -->
                <div id="sales-tab" class="tab-content p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Sales History</h3>
                    @if($customer->sales->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($customer->sales->take(10) as $sale)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $sale->invoice_number }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $sale->sale_date->format('d M, Y') }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{{ number_format($sale->total_amount, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    {{ ucfirst($sale->payment_status) }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-gray-500">No sales records found.</p>
                    @endif
                </div>

                <!-- Other tabs content would go here -->
                <div id="estimates-tab" class="tab-content p-6 hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Estimates History</h3>
                    <p class="text-gray-500">Estimates will be displayed here once the estimates module is implemented.</p>
                </div>

                <div id="repairs-tab" class="tab-content p-6 hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Repairs History</h3>
                    <p class="text-gray-500">Repairs will be displayed here once the repairs module is implemented.</p>
                </div>

                <div id="schemes-tab" class="tab-content p-6 hidden">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Saving Schemes</h3>
                    <p class="text-gray-500">Saving schemes will be displayed here once the schemes module is implemented.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tabName = this.getAttribute('data-tab');
                    
                    // Remove active class from all buttons
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-indigo-500', 'text-indigo-600', 'active');
                        btn.classList.add('border-transparent', 'text-gray-500');
                    });
                    
                    // Add active class to clicked button
                    this.classList.remove('border-transparent', 'text-gray-500');
                    this.classList.add('border-indigo-500', 'text-indigo-600', 'active');
                    
                    // Hide all tab contents
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    
                    // Show selected tab content
                    document.getElementById(tabName + '-tab').classList.remove('hidden');
                });
            });
        });
    </script>
</x-app-layout>
