<!-- Sidebar -->
<div x-data="sidebar()" class="flex">
    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen" 
         x-cloak 
         @click="sidebarOpen = false"
         class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden">
    </div>

    <!-- Sidebar -->
    <div :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'"
         class="fixed inset-y-0 left-0 z-40 w-64 bg-gradient-to-b from-indigo-900 to-indigo-800 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">

        <!-- Logo -->
        <div class="flex items-center justify-center h-16 px-4 bg-indigo-900 border-b border-indigo-700">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-gem text-yellow-400 text-2xl"></i>
                </div>
                <div class="ml-3">
                    <h1 class="text-xl font-bold text-white"><?php echo e(config('app.name', 'Jewel Pro')); ?></h1>
                    <p class="text-xs text-indigo-300">Jewelry Management</p>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mt-5 px-2 space-y-1 overflow-y-auto h-full pb-20 sidebar-scroll">
            <!-- Dashboard -->
            <a href="<?php echo e(route('dashboard')); ?>" 
               class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('dashboard') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                <i class="fas fa-tachometer-alt mr-3 text-lg"></i>
                Dashboard
            </a>

            <!-- Sales & Orders -->
            <div class="space-y-1">
                <div class="text-indigo-300 text-xs font-semibold uppercase tracking-wider px-3 py-2">
                    Sales & Orders
                </div>
                
                <a href="<?php echo e(route('sales.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('sales.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-shopping-cart mr-3 text-lg"></i>
                    Sales & Billing
                </a>

                <a href="<?php echo e(route('estimates.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('estimates.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-file-invoice mr-3 text-lg"></i>
                    Estimates
                </a>

                <a href="<?php echo e(route('old-gold-purchases.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('old-gold-purchases.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-coins mr-3 text-lg"></i>
                    Old Gold Exchange
                </a>
            </div>

            <!-- Inventory & Products -->
            <div class="space-y-1">
                <div class="text-indigo-300 text-xs font-semibold uppercase tracking-wider px-3 py-2">
                    Inventory
                </div>
                
                <a href="<?php echo e(route('products.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('products.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-box mr-3 text-lg"></i>
                    Products & Inventory
                </a>

                <a href="<?php echo e(route('barcodes.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('barcodes.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-barcode mr-3 text-lg"></i>
                    Barcodes & Labels
                </a>
            </div>

            <!-- Services -->
            <div class="space-y-1">
                <div class="text-indigo-300 text-xs font-semibold uppercase tracking-wider px-3 py-2">
                    Services
                </div>
                
                <a href="<?php echo e(route('repairs.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('repairs.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-tools mr-3 text-lg"></i>
                    Repairs & Services
                </a>

                <a href="<?php echo e(route('saving-schemes.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('saving-schemes.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-piggy-bank mr-3 text-lg"></i>
                    Saving Schemes
                </a>
            </div>

            <!-- Customers & Relations -->
            <div class="space-y-1">
                <div class="text-indigo-300 text-xs font-semibold uppercase tracking-wider px-3 py-2">
                    Customers
                </div>
                
                <a href="<?php echo e(route('customers.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('customers.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-users mr-3 text-lg"></i>
                    Customer Management
                </a>
            </div>

            <!-- Tools & Utilities -->
            <div class="space-y-1">
                <div class="text-indigo-300 text-xs font-semibold uppercase tracking-wider px-3 py-2">
                    Tools
                </div>
                
                <a href="<?php echo e(route('metal-rates.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('metal-rates.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-chart-line mr-3 text-lg"></i>
                    Metal Rates
                </a>

                <a href="<?php echo e(route('calculator')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('calculator*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-calculator mr-3 text-lg"></i>
                    Price Calculator
                </a>
            </div>

            <!-- Reports & Analytics -->
            <div class="space-y-1">
                <div class="text-indigo-300 text-xs font-semibold uppercase tracking-wider px-3 py-2">
                    Analytics
                </div>
                
                <a href="<?php echo e(route('reports.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('reports.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-chart-bar mr-3 text-lg"></i>
                    Reports & Analytics
                </a>
            </div>

            <!-- Settings -->
            <div class="space-y-1">
                <div class="text-indigo-300 text-xs font-semibold uppercase tracking-wider px-3 py-2">
                    System
                </div>
                
                <a href="<?php echo e(route('settings.index')); ?>" 
                   class="group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 <?php echo e(request()->routeIs('settings.*') ? 'bg-indigo-700 text-white' : 'text-indigo-100 hover:bg-indigo-700 hover:text-white'); ?>">
                    <i class="fas fa-cog mr-3 text-lg"></i>
                    Settings
                </a>
            </div>
        </nav>
    </div>
</div>
<?php /**PATH C:\proj\jewel-pro\resources\views/layouts/sidebar.blade.php ENDPATH**/ ?>