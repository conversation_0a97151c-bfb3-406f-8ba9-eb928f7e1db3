<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Repair') }} - #{{ $repair->repair_number }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('repairs.update', $repair) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <!-- Customer Selection -->
                            <div>
                                <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer</label>
                                <select name="customer_id" id="customer_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="">Select Customer</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}" {{ $repair->customer_id == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }} - {{ $customer->mobile }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('customer_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Item Description -->
                            <div>
                                <label for="item_description" class="block text-sm font-medium text-gray-700">Item Description</label>
                                <input type="text" name="item_description" id="item_description" value="{{ $repair->item_description }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                @error('item_description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Repair Type -->
                            <div>
                                <label for="repair_type" class="block text-sm font-medium text-gray-700">Repair Type</label>
                                <select name="repair_type" id="repair_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="">Select Repair Type</option>
                                    <option value="cleaning" {{ $repair->repair_type == 'cleaning' ? 'selected' : '' }}>Cleaning</option>
                                    <option value="polishing" {{ $repair->repair_type == 'polishing' ? 'selected' : '' }}>Polishing</option>
                                    <option value="stone_setting" {{ $repair->repair_type == 'stone_setting' ? 'selected' : '' }}>Stone Setting</option>
                                    <option value="chain_repair" {{ $repair->repair_type == 'chain_repair' ? 'selected' : '' }}>Chain Repair</option>
                                    <option value="ring_sizing" {{ $repair->repair_type == 'ring_sizing' ? 'selected' : '' }}>Ring Sizing</option>
                                    <option value="clasp_repair" {{ $repair->repair_type == 'clasp_repair' ? 'selected' : '' }}>Clasp Repair</option>
                                    <option value="other" {{ $repair->repair_type == 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('repair_type')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                                <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="received" {{ $repair->status == 'received' ? 'selected' : '' }}>Received</option>
                                    <option value="in_progress" {{ $repair->status == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                    <option value="completed" {{ $repair->status == 'completed' ? 'selected' : '' }}>Completed</option>
                                    <option value="delivered" {{ $repair->status == 'delivered' ? 'selected' : '' }}>Delivered</option>
                                    <option value="cancelled" {{ $repair->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                </select>
                                @error('status')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Received Date -->
                            <div>
                                <label for="received_date" class="block text-sm font-medium text-gray-700">Received Date</label>
                                <input type="date" name="received_date" id="received_date" value="{{ $repair->received_date->format('Y-m-d') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                @error('received_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Expected Completion Date -->
                            <div>
                                <label for="expected_completion_date" class="block text-sm font-medium text-gray-700">Expected Completion Date</label>
                                <input type="date" name="expected_completion_date" id="expected_completion_date" value="{{ $repair->expected_completion_date ? $repair->expected_completion_date->format('Y-m-d') : '' }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('expected_completion_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Actual Completion Date -->
                            <div>
                                <label for="actual_completion_date" class="block text-sm font-medium text-gray-700">Actual Completion Date</label>
                                <input type="date" name="actual_completion_date" id="actual_completion_date" value="{{ $repair->actual_completion_date ? $repair->actual_completion_date->format('Y-m-d') : '' }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('actual_completion_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Delivery Date -->
                            <div>
                                <label for="delivery_date" class="block text-sm font-medium text-gray-700">Delivery Date</label>
                                <input type="date" name="delivery_date" id="delivery_date" value="{{ $repair->delivery_date ? $repair->delivery_date->format('Y-m-d') : '' }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('delivery_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Estimated Cost -->
                            <div>
                                <label for="estimated_cost" class="block text-sm font-medium text-gray-700">Estimated Cost</label>
                                <input type="number" name="estimated_cost" id="estimated_cost" value="{{ $repair->estimated_cost }}" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('estimated_cost')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Actual Cost -->
                            <div>
                                <label for="actual_cost" class="block text-sm font-medium text-gray-700">Actual Cost</label>
                                <input type="number" name="actual_cost" id="actual_cost" value="{{ $repair->actual_cost }}" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('actual_cost')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Payment Status -->
                            <div>
                                <label for="payment_status" class="block text-sm font-medium text-gray-700">Payment Status</label>
                                <select name="payment_status" id="payment_status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="pending" {{ $repair->payment_status == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="partial" {{ $repair->payment_status == 'partial' ? 'selected' : '' }}>Partial</option>
                                    <option value="paid" {{ $repair->payment_status == 'paid' ? 'selected' : '' }}>Paid</option>
                                </select>
                                @error('payment_status')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Amount Paid -->
                            <div>
                                <label for="amount_paid" class="block text-sm font-medium text-gray-700">Amount Paid</label>
                                <input type="number" name="amount_paid" id="amount_paid" value="{{ $repair->amount_paid }}" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('amount_paid')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Problem Description -->
                        <div class="mb-6">
                            <label for="problem_description" class="block text-sm font-medium text-gray-700">Problem Description</label>
                            <textarea name="problem_description" id="problem_description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>{{ $repair->problem_description }}</textarea>
                            @error('problem_description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Work Done -->
                        <div class="mb-6">
                            <label for="work_done" class="block text-sm font-medium text-gray-700">Work Done</label>
                            <textarea name="work_done" id="work_done" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ $repair->work_done }}</textarea>
                            @error('work_done')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="mb-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea name="notes" id="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ $repair->notes }}</textarea>
                            @error('notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('repairs.show', $repair) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Repair
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
