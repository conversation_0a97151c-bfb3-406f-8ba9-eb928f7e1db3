<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Repair Details') }} - {{ $repair->repair_number }}
            </h2>
            <div class="flex space-x-2">
                @can('edit_repair')
                    <a href="{{ route('repairs.edit', $repair) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Edit Repair
                    </a>
                @endcan
                <a href="{{ route('repairs.receipt', $repair) }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Print Receipt
                </a>
                @if($repair->status === 'completed')
                    <a href="{{ route('repairs.delivery-slip', $repair) }}" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                        Delivery Slip
                    </a>
                @endif
                <a href="{{ route('repairs.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Repairs
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Repair Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Repair Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Repair Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Repair Number</dt>
                                    <dd class="text-sm text-gray-900 font-mono">{{ $repair->repair_number }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Item Name</dt>
                                    <dd class="text-sm text-gray-900 font-medium">{{ $repair->item_name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                                    <dd class="text-sm">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $repair->status_color }}">
                                            {{ ucfirst(str_replace('_', ' ', $repair->status)) }}
                                        </span>
                                        @if($repair->is_overdue)
                                            <span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                {{ $repair->days_overdue }} days overdue
                                            </span>
                                        @endif
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Created By</dt>
                                    <dd class="text-sm text-gray-900">{{ $repair->createdBy->name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Days in Repair</dt>
                                    <dd class="text-sm text-gray-900">{{ $repair->days_in_repair }} days</dd>
                                </div>
                            </dl>
                        </div>

                        <!-- Customer Details -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                            <dl class="space-y-2">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                                    <dd class="text-sm text-gray-900">{{ $repair->customer->name }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Mobile</dt>
                                    <dd class="text-sm text-gray-900">{{ $repair->customer->mobile }}</dd>
                                </div>
                                @if($repair->customer->email)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                                        <dd class="text-sm text-gray-900">{{ $repair->customer->email }}</dd>
                                    </div>
                                @endif
                                @if($repair->customer->address)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500">Address</dt>
                                        <dd class="text-sm text-gray-900">{{ $repair->customer->full_address }}</dd>
                                    </div>
                                @endif
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Issue Description and Notes -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Issue Description</h3>
                    <p class="text-sm text-gray-700 mb-6">{{ $repair->issue_description }}</p>

                    @if($repair->repair_notes)
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Repair Notes</h3>
                        <p class="text-sm text-gray-700">{{ $repair->repair_notes }}</p>
                    @endif
                </div>
            </div>

            <!-- Dates and Charges -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Important Dates -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Important Dates</h3>
                        <dl class="space-y-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Received Date</dt>
                                <dd class="text-sm text-gray-900">{{ $repair->received_date->format('d M, Y') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Promised Date</dt>
                                <dd class="text-sm {{ $repair->is_overdue ? 'text-red-600 font-semibold' : 'text-gray-900' }}">
                                    {{ $repair->promised_date->format('d M, Y') }}
                                </dd>
                            </div>
                            @if($repair->completed_date)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Completed Date</dt>
                                    <dd class="text-sm text-green-600">{{ $repair->completed_date->format('d M, Y') }}</dd>
                                </div>
                            @endif
                            @if($repair->delivered_date)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Delivered Date</dt>
                                    <dd class="text-sm text-blue-600">{{ $repair->delivered_date->format('d M, Y') }}</dd>
                                </div>
                            @endif
                        </dl>
                    </div>
                </div>

                <!-- Charges and Payment -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Charges & Payment</h3>
                        <dl class="space-y-2">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Estimated Charges</dt>
                                <dd class="text-sm text-gray-900">₹{{ number_format($repair->estimated_charges, 2) }}</dd>
                            </div>
                            @if($repair->actual_charges > 0)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Actual Charges</dt>
                                    <dd class="text-sm font-medium text-gray-900">₹{{ number_format($repair->actual_charges, 2) }}</dd>
                                </div>
                            @endif
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Payment Status</dt>
                                <dd class="text-sm">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $repair->payment_received ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $repair->payment_received ? 'Paid' : 'Pending' }}
                                    </span>
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- Receipt Image -->
            @if($repair->receipt_image_path)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Receipt Image</h3>
                        <div class="max-w-md">
                            <img src="{{ asset('storage/' . $repair->receipt_image_path) }}" 
                                 alt="Receipt Image" 
                                 class="w-full h-auto rounded-lg shadow-md">
                        </div>
                    </div>
                </div>
            @endif

            <!-- Status Update Form -->
            @can('edit_repair')
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Update Status</h3>
                        <form method="POST" action="{{ route('repairs.update-status', $repair) }}">
                            @csrf
                            @method('PATCH')
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                                    <select name="status" id="status" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="received" {{ $repair->status === 'received' ? 'selected' : '' }}>Received</option>
                                        <option value="in_progress" {{ $repair->status === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                        <option value="completed" {{ $repair->status === 'completed' ? 'selected' : '' }}>Completed</option>
                                        <option value="delivered" {{ $repair->status === 'delivered' ? 'selected' : '' }}>Delivered</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="actual_charges" class="block text-sm font-medium text-gray-700">Actual Charges (₹)</label>
                                    <input type="number" name="actual_charges" id="actual_charges" 
                                           value="{{ $repair->actual_charges > 0 ? $repair->actual_charges : $repair->estimated_charges }}" 
                                           step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>

                                <div class="flex items-end">
                                    <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                        Update Status
                                    </button>
                                </div>
                            </div>

                            <div class="mt-4">
                                <label for="repair_notes" class="block text-sm font-medium text-gray-700">Update Notes</label>
                                <textarea name="repair_notes" id="repair_notes" rows="3"
                                          placeholder="Add any updates or notes about the repair progress..."
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ $repair->repair_notes }}</textarea>
                            </div>
                        </form>
                    </div>
                </div>
            @endcan
        </div>
    </div>
</x-app-layout>
