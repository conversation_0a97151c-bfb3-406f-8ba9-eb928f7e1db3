<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Estimate') }} - #{{ $estimate->estimate_number }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('estimates.update', $estimate) }}" id="estimateForm">
                        @csrf
                        @method('PUT')
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <!-- Customer Selection -->
                            <div>
                                <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer</label>
                                <select name="customer_id" id="customer_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="">Select Customer</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}" {{ $estimate->customer_id == $customer->id ? 'selected' : '' }}>
                                            {{ $customer->name }} - {{ $customer->mobile }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('customer_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Estimate Date -->
                            <div>
                                <label for="estimate_date" class="block text-sm font-medium text-gray-700">Estimate Date</label>
                                <input type="date" name="estimate_date" id="estimate_date" value="{{ $estimate->estimate_date->format('Y-m-d') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                @error('estimate_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Valid Until -->
                            <div>
                                <label for="valid_until" class="block text-sm font-medium text-gray-700">Valid Until</label>
                                <input type="date" name="valid_until" id="valid_until" value="{{ $estimate->valid_until->format('Y-m-d') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                @error('valid_until')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                                <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                    <option value="pending" {{ $estimate->status == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="approved" {{ $estimate->status == 'approved' ? 'selected' : '' }}>Approved</option>
                                    <option value="rejected" {{ $estimate->status == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                </select>
                                @error('status')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Estimate Items -->
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Estimate Items</h3>
                                <button type="button" id="addItem" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    Add Item
                                </button>
                            </div>

                            <div id="itemsContainer">
                                @foreach($estimate->estimateItems as $index => $item)
                                <div class="item-row border border-gray-200 rounded-lg p-4 mb-4">
                                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700">Description</label>
                                            <input type="text" name="items[{{ $index }}][description]" value="{{ $item->description }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Metal Type</label>
                                            <select name="items[{{ $index }}][metal_type]" class="metal-type mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                                <option value="">Select</option>
                                                <option value="Gold" {{ $item->metal_type == 'Gold' ? 'selected' : '' }}>Gold</option>
                                                <option value="Silver" {{ $item->metal_type == 'Silver' ? 'selected' : '' }}>Silver</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Purity</label>
                                            <select name="items[{{ $index }}][purity]" class="purity mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                                <option value="">Select</option>
                                                <option value="22K" {{ $item->purity == '22K' ? 'selected' : '' }}>22K</option>
                                                <option value="18K" {{ $item->purity == '18K' ? 'selected' : '' }}>18K</option>
                                                <option value="916" {{ $item->purity == '916' ? 'selected' : '' }}>916</option>
                                                <option value="999" {{ $item->purity == '999' ? 'selected' : '' }}>999</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Weight (g)</label>
                                            <input type="number" name="items[{{ $index }}][weight]" value="{{ $item->weight }}" step="0.001" class="weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Making Charges</label>
                                            <input type="number" name="items[{{ $index }}][making_charges]" value="{{ $item->making_charges }}" step="0.01" class="making-charges mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                        </div>
                                    </div>
                                    <div class="mt-4 flex justify-between items-center">
                                        <div class="text-sm text-gray-600">
                                            Rate: ₹<span class="rate-display">{{ number_format($item->rate_per_gram, 2) }}</span>/g | 
                                            Amount: ₹<span class="amount-display">{{ number_format($item->amount, 2) }}</span>
                                        </div>
                                        <button type="button" class="remove-item bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded">
                                            Remove
                                        </button>
                                    </div>
                                    <input type="hidden" name="items[{{ $index }}][rate_per_gram]" value="{{ $item->rate_per_gram }}" class="rate-input">
                                    <input type="hidden" name="items[{{ $index }}][amount]" value="{{ $item->amount }}" class="amount-input">
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea name="notes" id="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ $estimate->notes }}</textarea>
                            @error('notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Totals -->
                        <div class="border-t pt-6 mb-6">
                            <div class="flex justify-end">
                                <div class="w-64">
                                    <dl class="space-y-2">
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Subtotal</dt>
                                            <dd class="text-sm text-gray-900">₹<span id="subtotal">{{ number_format($estimate->subtotal, 2) }}</span></dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Tax (3%)</dt>
                                            <dd class="text-sm text-gray-900">₹<span id="tax">{{ number_format($estimate->tax_amount, 2) }}</span></dd>
                                        </div>
                                        <div class="flex justify-between border-t pt-2">
                                            <dt class="text-base font-medium text-gray-900">Total</dt>
                                            <dd class="text-base font-medium text-gray-900">₹<span id="total">{{ number_format($estimate->total_amount, 2) }}</span></dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('estimates.show', $estimate) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Estimate
                            </button>
                        </div>

                        <input type="hidden" name="subtotal" id="subtotalInput" value="{{ $estimate->subtotal }}">
                        <input type="hidden" name="tax_amount" id="taxInput" value="{{ $estimate->tax_amount }}">
                        <input type="hidden" name="total_amount" id="totalInput" value="{{ $estimate->total_amount }}">
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Metal rates data
        const metalRates = @json($metalRates->keyBy(function($rate) { return $rate->metal_type . '-' . $rate->purity; }));
        let itemIndex = {{ count($estimate->estimateItems) }};

        // Add item functionality
        document.getElementById('addItem').addEventListener('click', function() {
            const container = document.getElementById('itemsContainer');
            const itemHtml = `
                <div class="item-row border border-gray-200 rounded-lg p-4 mb-4">
                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Description</label>
                            <input type="text" name="items[${itemIndex}][description]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Metal Type</label>
                            <select name="items[${itemIndex}][metal_type]" class="metal-type mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                <option value="">Select</option>
                                <option value="Gold">Gold</option>
                                <option value="Silver">Silver</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Purity</label>
                            <select name="items[${itemIndex}][purity]" class="purity mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                <option value="">Select</option>
                                <option value="22K">22K</option>
                                <option value="18K">18K</option>
                                <option value="916">916</option>
                                <option value="999">999</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Weight (g)</label>
                            <input type="number" name="items[${itemIndex}][weight]" step="0.001" class="weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Making Charges</label>
                            <input type="number" name="items[${itemIndex}][making_charges]" step="0.01" class="making-charges mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            Rate: ₹<span class="rate-display">0.00</span>/g | 
                            Amount: ₹<span class="amount-display">0.00</span>
                        </div>
                        <button type="button" class="remove-item bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded">
                            Remove
                        </button>
                    </div>
                    <input type="hidden" name="items[${itemIndex}][rate_per_gram]" value="0" class="rate-input">
                    <input type="hidden" name="items[${itemIndex}][amount]" value="0" class="amount-input">
                </div>
            `;
            container.insertAdjacentHTML('beforeend', itemHtml);
            itemIndex++;
            attachEventListeners();
        });

        // Remove item functionality
        function attachEventListeners() {
            document.querySelectorAll('.remove-item').forEach(button => {
                button.addEventListener('click', function() {
                    this.closest('.item-row').remove();
                    calculateTotals();
                });
            });

            // Calculate amounts when inputs change
            document.querySelectorAll('.metal-type, .purity, .weight, .making-charges').forEach(input => {
                input.addEventListener('change', function() {
                    calculateItemAmount(this.closest('.item-row'));
                });
            });
        }

        function calculateItemAmount(row) {
            const metalType = row.querySelector('.metal-type').value;
            const purity = row.querySelector('.purity').value;
            const weight = parseFloat(row.querySelector('.weight').value) || 0;
            const makingCharges = parseFloat(row.querySelector('.making-charges').value) || 0;

            if (metalType && purity && weight > 0) {
                const rateKey = metalType + '-' + purity;
                const rate = metalRates[rateKey] ? metalRates[rateKey].rate_per_gram : 0;
                const amount = (weight * rate) + makingCharges;

                row.querySelector('.rate-display').textContent = rate.toFixed(2);
                row.querySelector('.amount-display').textContent = amount.toFixed(2);
                row.querySelector('.rate-input').value = rate;
                row.querySelector('.amount-input').value = amount;

                calculateTotals();
            }
        }

        function calculateTotals() {
            let subtotal = 0;
            document.querySelectorAll('.amount-input').forEach(input => {
                subtotal += parseFloat(input.value) || 0;
            });

            const taxAmount = subtotal * 0.03;
            const total = subtotal + taxAmount;

            document.getElementById('subtotal').textContent = subtotal.toFixed(2);
            document.getElementById('tax').textContent = taxAmount.toFixed(2);
            document.getElementById('total').textContent = total.toFixed(2);

            document.getElementById('subtotalInput').value = subtotal;
            document.getElementById('taxInput').value = taxAmount;
            document.getElementById('totalInput').value = total;
        }

        // Initialize event listeners
        attachEventListeners();
    </script>
</x-app-layout>
