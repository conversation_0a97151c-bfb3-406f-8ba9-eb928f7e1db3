<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'category',
        'subcategory',
        'metal_type',
        'purity',
        'gross_weight',
        'net_weight',
        'stone_weight',
        'wastage_percentage',
        'making_charges',
        'stone_charges',
        'hsn_code',
        'huid_number',
        'barcode',
        'cost_price',
        'selling_price',
        'quantity',
        'size',
        'description',
        'image_path',
        'has_stones',
        'stone_details',
        'status',
    ];

    protected $casts = [
        'gross_weight' => 'decimal:3',
        'net_weight' => 'decimal:3',
        'stone_weight' => 'decimal:3',
        'wastage_percentage' => 'decimal:2',
        'making_charges' => 'decimal:2',
        'stone_charges' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'has_stones' => 'boolean',
        'stone_details' => 'array',
    ];

    // Relationships
    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }

    // Accessors & Mutators
    public function getProfitAttribute()
    {
        return $this->selling_price - $this->cost_price;
    }

    public function getProfitMarginAttribute()
    {
        if ($this->cost_price == 0) return 0;
        return (($this->selling_price - $this->cost_price) / $this->cost_price) * 100;
    }

    public function getWastageAmountAttribute()
    {
        return ($this->net_weight * $this->wastage_percentage) / 100;
    }

    public function getTotalWeightAttribute()
    {
        return $this->net_weight + $this->wastage_amount;
    }

    // Scopes
    public function scopeInStock($query)
    {
        return $query->where('status', 'in_stock');
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByMetalType($query, $metalType)
    {
        return $query->where('metal_type', $metalType);
    }

    public function scopeWithStones($query)
    {
        return $query->where('has_stones', true);
    }

    // Boot method to generate barcode
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->barcode)) {
                $product->barcode = 'JP' . strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Generate unique barcode
     */
    public static function generateUniqueBarcode()
    {
        do {
            // Generate 13-digit barcode starting with 2 (for internal use)
            $barcode = '2' . str_pad(mt_rand(0, 999999999999), 12, '0', STR_PAD_LEFT);
        } while (static::where('barcode', $barcode)->exists());

        return $barcode;
    }
}
