<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Record Old Gold Purchase') }}
            </h2>
            <a href="{{ route('old-gold-purchases.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Old Gold Purchases
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('old-gold-purchases.store') }}" id="oldGoldForm">
                        @csrf

                        <!-- Customer Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                            <div class="grid grid-cols-1 gap-6">
                                <div>
                                    <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer *</label>
                                    <select name="customer_id" id="customer_id" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Customer</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}" {{ old('customer_id') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }} - {{ $customer->mobile }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('customer_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Metal Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Metal Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="metal_type" class="block text-sm font-medium text-gray-700">Metal Type *</label>
                                    <select name="metal_type" id="metal_type" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Metal Type</option>
                                        <option value="Gold" {{ old('metal_type') === 'Gold' ? 'selected' : '' }}>Gold</option>
                                        <option value="Silver" {{ old('metal_type') === 'Silver' ? 'selected' : '' }}>Silver</option>
                                    </select>
                                    @error('metal_type')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="purity" class="block text-sm font-medium text-gray-700">Purity *</label>
                                    <select name="purity" id="purity" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Purity</option>
                                        <!-- Options will be populated by JavaScript based on metal type -->
                                    </select>
                                    @error('purity')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Weight Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Weight Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="gross_weight" class="block text-sm font-medium text-gray-700">Gross Weight (grams) *</label>
                                    <input type="number" name="gross_weight" id="gross_weight" value="{{ old('gross_weight') }}" 
                                           step="0.001" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('gross_weight')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="stone_weight" class="block text-sm font-medium text-gray-700">Stone Weight (grams)</label>
                                    <input type="number" name="stone_weight" id="stone_weight" value="{{ old('stone_weight', 0) }}" 
                                           step="0.001" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('stone_weight')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="net_weight" class="block text-sm font-medium text-gray-700">Net Weight (grams)</label>
                                    <input type="number" id="net_weight" readonly
                                           class="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm">
                                    <p class="mt-1 text-xs text-gray-500">Calculated: Gross - Stone Weight</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
                                <div>
                                    <label for="melting_loss_percentage" class="block text-sm font-medium text-gray-700">Melting Loss (%)</label>
                                    <input type="number" name="melting_loss_percentage" id="melting_loss_percentage" value="{{ old('melting_loss_percentage', 0) }}" 
                                           step="0.01" min="0" max="100"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('melting_loss_percentage')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="melting_loss_weight" class="block text-sm font-medium text-gray-700">Melting Loss Weight (grams)</label>
                                    <input type="number" id="melting_loss_weight" readonly
                                           class="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm">
                                    <p class="mt-1 text-xs text-gray-500">Calculated: Net × Loss %</p>
                                </div>

                                <div>
                                    <label for="final_weight" class="block text-sm font-medium text-gray-700">Final Weight (grams)</label>
                                    <input type="number" id="final_weight" readonly
                                           class="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm">
                                    <p class="mt-1 text-xs text-gray-500">Calculated: Net - Loss Weight</p>
                                </div>
                            </div>
                        </div>

                        <!-- Rate and Amount Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Rate & Amount Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="rate_per_gram" class="block text-sm font-medium text-gray-700">Rate per Gram (₹) *</label>
                                    <input type="number" name="rate_per_gram" id="rate_per_gram" value="{{ old('rate_per_gram') }}" 
                                           step="0.01" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <p class="mt-1 text-xs text-gray-500" id="current_rate_display"></p>
                                    @error('rate_per_gram')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="total_amount" class="block text-sm font-medium text-gray-700">Total Amount (₹)</label>
                                    <input type="number" id="total_amount" readonly
                                           class="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm">
                                    <p class="mt-1 text-xs text-gray-500">Calculated: Final Weight × Rate</p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                                    <div class="space-y-2">
                                        <label class="flex items-center">
                                            <input type="radio" name="payment_method" value="cash" class="payment-method-radio" checked>
                                            <span class="ml-2 text-sm text-gray-700">Cash Payment</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" name="payment_method" value="voucher" class="payment-method-radio">
                                            <span class="ml-2 text-sm text-gray-700">Store Voucher</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" name="payment_method" value="mixed" class="payment-method-radio">
                                            <span class="ml-2 text-sm text-gray-700">Mixed Payment</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4" id="payment_details">
                                <div id="cash_payment_div">
                                    <label for="cash_paid" class="block text-sm font-medium text-gray-700">Cash Paid (₹)</label>
                                    <input type="number" name="cash_paid" id="cash_paid" value="{{ old('cash_paid', 0) }}" 
                                           step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('cash_paid')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div id="voucher_amount_div" style="display: none;">
                                    <label for="voucher_amount" class="block text-sm font-medium text-gray-700">Voucher Amount (₹)</label>
                                    <input type="number" name="voucher_amount" id="voucher_amount" value="{{ old('voucher_amount', 0) }}" 
                                           step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('voucher_amount')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-8">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea name="notes" id="notes" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('notes') }}</textarea>
                            @error('notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('old-gold-purchases.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Record Purchase
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        const metalRates = @json($metalRates);
        
        document.addEventListener('DOMContentLoaded', function() {
            const metalTypeSelect = document.getElementById('metal_type');
            const puritySelect = document.getElementById('purity');
            const grossWeightInput = document.getElementById('gross_weight');
            const stoneWeightInput = document.getElementById('stone_weight');
            const netWeightInput = document.getElementById('net_weight');
            const meltingLossPercentageInput = document.getElementById('melting_loss_percentage');
            const meltingLossWeightInput = document.getElementById('melting_loss_weight');
            const finalWeightInput = document.getElementById('final_weight');
            const ratePerGramInput = document.getElementById('rate_per_gram');
            const totalAmountInput = document.getElementById('total_amount');
            const currentRateDisplay = document.getElementById('current_rate_display');
            const paymentMethodRadios = document.querySelectorAll('.payment-method-radio');
            const cashPaymentDiv = document.getElementById('cash_payment_div');
            const voucherAmountDiv = document.getElementById('voucher_amount_div');
            const cashPaidInput = document.getElementById('cash_paid');
            const voucherAmountInput = document.getElementById('voucher_amount');

            // Purity options based on metal type
            const purityOptions = {
                'Gold': ['22K', '18K', '14K'],
                'Silver': ['925', '999']
            };

            // Update purity options when metal type changes
            metalTypeSelect.addEventListener('change', function() {
                const selectedMetal = this.value;
                puritySelect.innerHTML = '<option value="">Select Purity</option>';
                
                if (selectedMetal && purityOptions[selectedMetal]) {
                    purityOptions[selectedMetal].forEach(purity => {
                        const option = document.createElement('option');
                        option.value = purity;
                        option.textContent = purity;
                        puritySelect.appendChild(option);
                    });
                }
                
                updateCurrentRate();
            });

            // Update current rate display when purity changes
            puritySelect.addEventListener('change', updateCurrentRate);

            // Calculate weights and amounts
            [grossWeightInput, stoneWeightInput, meltingLossPercentageInput, ratePerGramInput].forEach(input => {
                input.addEventListener('input', calculateAmounts);
            });

            // Payment method change
            paymentMethodRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    const method = this.value;
                    
                    if (method === 'cash') {
                        cashPaymentDiv.style.display = 'block';
                        voucherAmountDiv.style.display = 'none';
                        cashPaidInput.value = totalAmountInput.value || 0;
                        voucherAmountInput.value = 0;
                    } else if (method === 'voucher') {
                        cashPaymentDiv.style.display = 'none';
                        voucherAmountDiv.style.display = 'block';
                        cashPaidInput.value = 0;
                        voucherAmountInput.value = totalAmountInput.value || 0;
                    } else if (method === 'mixed') {
                        cashPaymentDiv.style.display = 'block';
                        voucherAmountDiv.style.display = 'block';
                        const halfAmount = (parseFloat(totalAmountInput.value) || 0) / 2;
                        cashPaidInput.value = halfAmount.toFixed(2);
                        voucherAmountInput.value = halfAmount.toFixed(2);
                    }
                });
            });

            function updateCurrentRate() {
                const metalType = metalTypeSelect.value;
                const purity = puritySelect.value;
                
                if (metalType && purity) {
                    const rateKey = `${metalType}.${purity}`;
                    const rate = metalRates[rateKey];
                    
                    if (rate) {
                        currentRateDisplay.textContent = `Current market rate: ₹${rate.rate_per_gram}/gram`;
                        ratePerGramInput.value = rate.rate_per_gram;
                        calculateAmounts();
                    } else {
                        currentRateDisplay.textContent = 'No current rate available';
                    }
                } else {
                    currentRateDisplay.textContent = '';
                }
            }

            function calculateAmounts() {
                const grossWeight = parseFloat(grossWeightInput.value) || 0;
                const stoneWeight = parseFloat(stoneWeightInput.value) || 0;
                const netWeight = grossWeight - stoneWeight;
                
                const meltingLossPercentage = parseFloat(meltingLossPercentageInput.value) || 0;
                const meltingLossWeight = (netWeight * meltingLossPercentage) / 100;
                const finalWeight = netWeight - meltingLossWeight;
                
                const ratePerGram = parseFloat(ratePerGramInput.value) || 0;
                const totalAmount = finalWeight * ratePerGram;
                
                netWeightInput.value = netWeight.toFixed(3);
                meltingLossWeightInput.value = meltingLossWeight.toFixed(3);
                finalWeightInput.value = finalWeight.toFixed(3);
                totalAmountInput.value = totalAmount.toFixed(2);
                
                // Update payment amounts based on selected method
                const selectedPaymentMethod = document.querySelector('.payment-method-radio:checked').value;
                if (selectedPaymentMethod === 'cash') {
                    cashPaidInput.value = totalAmount.toFixed(2);
                } else if (selectedPaymentMethod === 'voucher') {
                    voucherAmountInput.value = totalAmount.toFixed(2);
                } else if (selectedPaymentMethod === 'mixed') {
                    const halfAmount = totalAmount / 2;
                    cashPaidInput.value = halfAmount.toFixed(2);
                    voucherAmountInput.value = halfAmount.toFixed(2);
                }
            }

            // Initialize calculations
            calculateAmounts();
        });
    </script>
</x-app-layout>
