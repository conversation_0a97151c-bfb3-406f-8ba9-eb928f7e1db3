<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estimate - {{ $estimate->estimate_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .company-details {
            font-size: 11px;
            color: #666;
        }
        .estimate-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            color: #333;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
        }
        .details-section {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .details-left, .details-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        .details-right {
            text-align: right;
        }
        .section-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            color: #333;
        }
        .customer-details, .estimate-info {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .items-table th, .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .items-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .items-table .text-right {
            text-align: right;
        }
        .totals {
            margin-top: 20px;
            float: right;
            width: 300px;
        }
        .totals table {
            width: 100%;
            border-collapse: collapse;
        }
        .totals td {
            padding: 5px 10px;
            border-bottom: 1px solid #eee;
        }
        .totals .total-row {
            font-weight: bold;
            font-size: 14px;
            border-top: 2px solid #333;
        }
        .validity-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .terms {
            margin-top: 30px;
            font-size: 10px;
            color: #666;
        }
        .signature {
            margin-top: 40px;
            text-align: right;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-pending { background: #fff3e0; color: #f57c00; }
        .status-approved { background: #e8f5e8; color: #388e3c; }
        .status-rejected { background: #ffebee; color: #d32f2f; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Jewel Pro') }}</div>
        <div class="company-details">
            Premium Jewelry Store<br>
            123 Main Street, City, State - 123456<br>
            Phone: +91 98765 43210 | Email: <EMAIL><br>
            GSTIN: 29ABCDE1234F1Z5
        </div>
    </div>

    <div class="estimate-title">JEWELRY ESTIMATE</div>

    <div class="details-section">
        <div class="details-left">
            <div class="section-title">Customer Details:</div>
            <div class="customer-details">
                <strong>{{ $estimate->customer->name }}</strong><br>
                @if($estimate->customer->address)
                    {{ $estimate->customer->address }}<br>
                @endif
                Mobile: {{ $estimate->customer->mobile }}<br>
                @if($estimate->customer->email)
                    Email: {{ $estimate->customer->email }}<br>
                @endif
                @if($estimate->customer->gstin)
                    GSTIN: {{ $estimate->customer->gstin }}
                @endif
            </div>
        </div>
        <div class="details-right">
            <div class="section-title">Estimate Details:</div>
            <div class="estimate-info">
                <strong>Estimate #:</strong> {{ $estimate->estimate_number }}<br>
                <strong>Date:</strong> {{ $estimate->estimate_date->format('d/m/Y') }}<br>
                <strong>Valid Until:</strong> {{ $estimate->valid_until->format('d/m/Y') }}<br>
                <strong>Status:</strong> 
                <span class="status-badge status-{{ $estimate->status }}">{{ ucfirst($estimate->status) }}</span><br>
                <strong>Prepared By:</strong> {{ $estimate->createdBy->name ?? 'N/A' }}
            </div>
        </div>
    </div>

    <div class="validity-info">
        <strong>Important:</strong> This estimate is valid until {{ $estimate->valid_until->format('d/m/Y') }}. 
        Prices may vary after this date due to fluctuations in metal rates.
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 5%">#</th>
                <th style="width: 35%">Item Description</th>
                <th style="width: 15%">Metal Type</th>
                <th style="width: 12%">Weight (g)</th>
                <th style="width: 12%">Rate/g</th>
                <th style="width: 12%">Making</th>
                <th style="width: 9%" class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            @foreach($estimate->estimateItems as $index => $item)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>
                    <strong>{{ $item->description }}</strong>
                    @if($item->specifications)
                        <br><small style="color: #666;">{{ $item->specifications }}</small>
                    @endif
                </td>
                <td>{{ $item->metal_type }} {{ $item->purity }}</td>
                <td class="text-right">{{ number_format($item->weight, 3) }}</td>
                <td class="text-right">₹{{ number_format($item->rate_per_gram, 2) }}</td>
                <td class="text-right">₹{{ number_format($item->making_charges, 2) }}</td>
                <td class="text-right">₹{{ number_format($item->amount, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="totals">
        <table>
            <tr>
                <td>Subtotal:</td>
                <td class="text-right">₹{{ number_format($estimate->subtotal, 2) }}</td>
            </tr>
            <tr>
                <td>Tax ({{ $estimate->tax_percentage }}%):</td>
                <td class="text-right">₹{{ number_format($estimate->tax_amount, 2) }}</td>
            </tr>
            <tr class="total-row">
                <td><strong>Total Estimate:</strong></td>
                <td class="text-right"><strong>₹{{ number_format($estimate->total_amount, 2) }}</strong></td>
            </tr>
        </table>
    </div>

    <div style="clear: both;"></div>

    @if($estimate->notes)
    <div style="margin-top: 30px; padding: 15px; background: #f9f9f9; border-radius: 5px;">
        <div class="section-title">Additional Notes:</div>
        <p style="margin: 0;">{{ $estimate->notes }}</p>
    </div>
    @endif

    <div class="terms">
        <strong>Terms & Conditions:</strong><br>
        1. This is an estimate only. Final prices may vary based on actual work required.<br>
        2. Metal rates are subject to market fluctuations and may change daily.<br>
        3. Making charges may vary based on design complexity and finishing requirements.<br>
        4. Advance payment of 50% is required to confirm the order.<br>
        5. Delivery time: 7-15 working days from order confirmation.<br>
        6. All custom jewelry orders are final and non-refundable.<br>
        7. Prices include applicable taxes as per current GST rates.<br>
        8. This estimate is valid for {{ $estimate->valid_until->diffInDays($estimate->estimate_date) }} days from the date of issue.
    </div>

    <div class="signature">
        <div style="margin-top: 50px; border-top: 1px solid #333; width: 200px; margin-left: auto;">
            <div style="text-align: center; margin-top: 10px;">
                <strong>Authorized Signatory</strong>
            </div>
        </div>
    </div>

    <div class="footer">
        <strong>Thank you for considering our services!</strong><br>
        For any queries or to place an order, please contact us at +91 98765 43210<br>
        <small>This is a computer-generated estimate and does not require a signature.</small>
    </div>
</body>
</html>
