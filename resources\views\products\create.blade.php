<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Add New Product') }}
            </h2>
            <a href="{{ route('products.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Inventory
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('products.store') }}" enctype="multipart/form-data" id="productForm">
                        @csrf

                        <!-- Basic Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700">Product Name *</label>
                                    <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('name')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-700">Category *</label>
                                    <select name="category" id="category" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Category</option>
                                        <option value="Ring" {{ old('category') === 'Ring' ? 'selected' : '' }}>Ring</option>
                                        <option value="Necklace" {{ old('category') === 'Necklace' ? 'selected' : '' }}>Necklace</option>
                                        <option value="Earrings" {{ old('category') === 'Earrings' ? 'selected' : '' }}>Earrings</option>
                                        <option value="Bracelet" {{ old('category') === 'Bracelet' ? 'selected' : '' }}>Bracelet</option>
                                        <option value="Chain" {{ old('category') === 'Chain' ? 'selected' : '' }}>Chain</option>
                                        <option value="Pendant" {{ old('category') === 'Pendant' ? 'selected' : '' }}>Pendant</option>
                                        <option value="Bangles" {{ old('category') === 'Bangles' ? 'selected' : '' }}>Bangles</option>
                                        <option value="Anklet" {{ old('category') === 'Anklet' ? 'selected' : '' }}>Anklet</option>
                                        <option value="Nose Pin" {{ old('category') === 'Nose Pin' ? 'selected' : '' }}>Nose Pin</option>
                                        <option value="Other" {{ old('category') === 'Other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    @error('category')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="subcategory" class="block text-sm font-medium text-gray-700">Subcategory</label>
                                    <input type="text" name="subcategory" id="subcategory" value="{{ old('subcategory') }}"
                                           placeholder="e.g., Gold Ring, Diamond Ring"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('subcategory')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="size" class="block text-sm font-medium text-gray-700">Size</label>
                                    <input type="text" name="size" id="size" value="{{ old('size') }}"
                                           placeholder="e.g., 16, M, L"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('size')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Metal Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Metal Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="metal_type" class="block text-sm font-medium text-gray-700">Metal Type *</label>
                                    <select name="metal_type" id="metal_type" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Metal Type</option>
                                        <option value="Gold" {{ old('metal_type') === 'Gold' ? 'selected' : '' }}>Gold</option>
                                        <option value="Silver" {{ old('metal_type') === 'Silver' ? 'selected' : '' }}>Silver</option>
                                        <option value="Platinum" {{ old('metal_type') === 'Platinum' ? 'selected' : '' }}>Platinum</option>
                                    </select>
                                    @error('metal_type')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="purity" class="block text-sm font-medium text-gray-700">Purity *</label>
                                    <select name="purity" id="purity" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Purity</option>
                                        <!-- Options will be populated by JavaScript based on metal type -->
                                    </select>
                                    @error('purity')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Weight Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Weight Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="gross_weight" class="block text-sm font-medium text-gray-700">Gross Weight (grams) *</label>
                                    <input type="number" name="gross_weight" id="gross_weight" value="{{ old('gross_weight') }}" 
                                           step="0.001" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('gross_weight')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="net_weight" class="block text-sm font-medium text-gray-700">Net Weight (grams) *</label>
                                    <input type="number" name="net_weight" id="net_weight" value="{{ old('net_weight') }}" 
                                           step="0.001" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('net_weight')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="wastage_percentage" class="block text-sm font-medium text-gray-700">Wastage (%)</label>
                                    <input type="number" name="wastage_percentage" id="wastage_percentage" value="{{ old('wastage_percentage', 0) }}" 
                                           step="0.01" min="0" max="100"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('wastage_percentage')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Stone Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Stone Information</h3>
                            <div class="mb-4">
                                <label for="has_stones" class="flex items-center">
                                    <input type="checkbox" name="has_stones" id="has_stones" value="1" {{ old('has_stones') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">This product has stones</span>
                                </label>
                            </div>
                            
                            <div id="stone-details" class="grid grid-cols-1 md:grid-cols-2 gap-6" style="display: none;">
                                <div>
                                    <label for="stone_weight" class="block text-sm font-medium text-gray-700">Stone Weight (carats)</label>
                                    <input type="number" name="stone_weight" id="stone_weight" value="{{ old('stone_weight', 0) }}" 
                                           step="0.001" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('stone_weight')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="stone_charges" class="block text-sm font-medium text-gray-700">Stone Charges (₹)</label>
                                    <input type="number" name="stone_charges" id="stone_charges" value="{{ old('stone_charges', 0) }}" 
                                           step="0.01" min="0"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('stone_charges')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label for="making_charges" class="block text-sm font-medium text-gray-700">Making Charges (₹) *</label>
                                    <input type="number" name="making_charges" id="making_charges" value="{{ old('making_charges') }}" 
                                           step="0.01" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('making_charges')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="cost_price" class="block text-sm font-medium text-gray-700">Cost Price (₹) *</label>
                                    <input type="number" name="cost_price" id="cost_price" value="{{ old('cost_price') }}" 
                                           step="0.01" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('cost_price')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="selling_price" class="block text-sm font-medium text-gray-700">Selling Price (₹) *</label>
                                    <input type="number" name="selling_price" id="selling_price" value="{{ old('selling_price') }}" 
                                           step="0.01" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('selling_price')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Product Details -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Product Details</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="quantity" class="block text-sm font-medium text-gray-700">Quantity *</label>
                                    <input type="number" name="quantity" id="quantity" value="{{ old('quantity', 1) }}" 
                                           min="1" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('quantity')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="hsn_code" class="block text-sm font-medium text-gray-700">HSN Code</label>
                                    <input type="text" name="hsn_code" id="hsn_code" value="{{ old('hsn_code', '71131900') }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('hsn_code')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="huid_number" class="block text-sm font-medium text-gray-700">HUID Number</label>
                                    <input type="text" name="huid_number" id="huid_number" value="{{ old('huid_number') }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('huid_number')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="barcode" class="block text-sm font-medium text-gray-700">Barcode</label>
                                    <input type="text" name="barcode" id="barcode" value="{{ old('barcode') }}"
                                           placeholder="Leave empty to auto-generate"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('barcode')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="md:col-span-2">
                                    <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                    <textarea name="description" id="description" rows="3"
                                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('description') }}</textarea>
                                    @error('description')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="md:col-span-2">
                                    <label for="image" class="block text-sm font-medium text-gray-700">Product Image</label>
                                    <input type="file" name="image" id="image" accept=".jpg,.jpeg,.png"
                                           class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                    <p class="mt-1 text-sm text-gray-500">Upload JPG, JPEG, or PNG files (max 2MB)</p>
                                    @error('image')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('products.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create Product
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const metalTypeSelect = document.getElementById('metal_type');
            const puritySelect = document.getElementById('purity');
            const hasStonesCheckbox = document.getElementById('has_stones');
            const stoneDetails = document.getElementById('stone-details');

            // Purity options based on metal type
            const purityOptions = {
                'Gold': ['22K', '18K', '14K', '10K'],
                'Silver': ['925', '999'],
                'Platinum': ['950', '900']
            };

            // Update purity options when metal type changes
            metalTypeSelect.addEventListener('change', function() {
                const selectedMetal = this.value;
                puritySelect.innerHTML = '<option value="">Select Purity</option>';
                
                if (selectedMetal && purityOptions[selectedMetal]) {
                    purityOptions[selectedMetal].forEach(purity => {
                        const option = document.createElement('option');
                        option.value = purity;
                        option.textContent = purity;
                        puritySelect.appendChild(option);
                    });
                }
            });

            // Show/hide stone details
            hasStonesCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    stoneDetails.style.display = 'grid';
                } else {
                    stoneDetails.style.display = 'none';
                }
            });

            // Initialize stone details visibility
            if (hasStonesCheckbox.checked) {
                stoneDetails.style.display = 'grid';
            }

            // Initialize purity options if metal type is already selected
            if (metalTypeSelect.value) {
                metalTypeSelect.dispatchEvent(new Event('change'));
            }
        });
    </script>
</x-app-layout>
