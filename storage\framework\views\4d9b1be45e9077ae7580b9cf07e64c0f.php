<!-- Top Navigation Bar -->
<header x-data="sidebar()" class="bg-white shadow-sm border-b border-gray-200 relative z-50">
    <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
        <!-- Mobile menu button -->
        <div class="flex items-center lg:hidden">
            <button @click="toggleSidebar()" 
                    class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500">
                <span class="sr-only">Open main menu</span>
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>

        <!-- Page title and breadcrumb -->
        <div class="flex-1 min-w-0 lg:ml-0 ml-4">
            <div class="flex items-center">
                <h1 class="text-lg font-semibold text-gray-900 truncate">
                    <?php if(request()->routeIs('dashboard')): ?>
                        Dashboard
                    <?php elseif(request()->routeIs('customers.*')): ?>
                        Customer Management
                    <?php elseif(request()->routeIs('products.*')): ?>
                        Products & Inventory
                    <?php elseif(request()->routeIs('sales.*')): ?>
                        Sales & Billing
                    <?php elseif(request()->routeIs('old-gold-purchases.*')): ?>
                        Old Gold Exchange
                    <?php elseif(request()->routeIs('estimates.*')): ?>
                        Estimates
                    <?php elseif(request()->routeIs('repairs.*')): ?>
                        Repairs & Services
                    <?php elseif(request()->routeIs('barcodes.*')): ?>
                        Barcodes & Labels
                    <?php elseif(request()->routeIs('metal-rates.*')): ?>
                        Metal Rates
                    <?php elseif(request()->routeIs('calculator*')): ?>
                        Price Calculator
                    <?php elseif(request()->routeIs('saving-schemes.*')): ?>
                        Saving Schemes
                    <?php elseif(request()->routeIs('reports.*')): ?>
                        Reports & Analytics
                    <?php elseif(request()->routeIs('settings.*')): ?>
                        Settings
                    <?php else: ?>
                        <?php echo e(config('app.name', 'Jewel Pro')); ?>

                    <?php endif; ?>
                </h1>
                
                <!-- Breadcrumb -->
                <nav class="hidden sm:flex ml-4" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-2 text-sm text-gray-500">
                        <li>
                            <a href="<?php echo e(route('dashboard')); ?>" class="hover:text-gray-700">
                                <i class="fas fa-home"></i>
                            </a>
                        </li>
                        <?php if(!request()->routeIs('dashboard')): ?>
                            <li>
                                <i class="fas fa-chevron-right text-xs"></i>
                            </li>
                            <li class="text-gray-900 font-medium">
                                <?php if(request()->routeIs('customers.*')): ?>
                                    Customers
                                <?php elseif(request()->routeIs('products.*')): ?>
                                    Inventory
                                <?php elseif(request()->routeIs('sales.*')): ?>
                                    Sales
                                <?php elseif(request()->routeIs('old-gold-purchases.*')): ?>
                                    Old Gold
                                <?php elseif(request()->routeIs('estimates.*')): ?>
                                    Estimates
                                <?php elseif(request()->routeIs('repairs.*')): ?>
                                    Repairs
                                <?php elseif(request()->routeIs('barcodes.*')): ?>
                                    Barcodes
                                <?php elseif(request()->routeIs('metal-rates.*')): ?>
                                    Metal Rates
                                <?php elseif(request()->routeIs('calculator*')): ?>
                                    Calculator
                                <?php elseif(request()->routeIs('saving-schemes.*')): ?>
                                    Schemes
                                <?php elseif(request()->routeIs('reports.*')): ?>
                                    Reports
                                <?php elseif(request()->routeIs('settings.*')): ?>
                                    Settings
                                <?php endif; ?>
                            </li>
                        <?php endif; ?>
                    </ol>
                </nav>
            </div>
        </div>

        <!-- Right side items -->
        <div class="flex items-center space-x-4">
            <!-- Quick Actions -->
            <div class="hidden md:flex items-center space-x-2">
                <!-- Quick Sale Button -->
                <a href="<?php echo e(route('sales.create')); ?>" 
                   class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    Quick Sale
                </a>
                
                <!-- Notifications -->
                <button class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <span class="sr-only">View notifications</span>
                    <i class="fas fa-bell text-lg"></i>
                    <!-- Notification badge -->
                    <span class="absolute -mt-1 -mr-1 px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full">3</span>
                </button>
            </div>

            <!-- User menu -->
            <div x-data="{ open: false }" class="relative">
                <div>
                    <button @click="open = !open" 
                            class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" 
                            id="user-menu-button" 
                            aria-expanded="false" 
                            aria-haspopup="true">
                        <span class="sr-only">Open user menu</span>
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center">
                                    <span class="text-sm font-medium text-white">
                                        <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                                    </span>
                                </div>
                            </div>
                            <div class="hidden md:block">
                                <div class="text-sm font-medium text-gray-700"><?php echo e(Auth::user()->name); ?></div>
                                <div class="text-xs text-gray-500"><?php echo e(Auth::user()->email); ?></div>
                            </div>
                            <i class="fas fa-chevron-down text-xs text-gray-400"></i>
                        </div>
                    </button>
                </div>

                <!-- User dropdown menu -->
                <div x-show="open" 
                     @click.away="open = false"
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50" 
                     role="menu" 
                     aria-orientation="vertical" 
                     aria-labelledby="user-menu-button" 
                     tabindex="-1">
                    
                    <a href="<?php echo e(route('profile.edit')); ?>" 
                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" 
                       role="menuitem" 
                       tabindex="-1">
                        <i class="fas fa-user mr-3"></i>
                        Your Profile
                    </a>
                    
                    <a href="<?php echo e(route('settings.index')); ?>" 
                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" 
                       role="menuitem" 
                       tabindex="-1">
                        <i class="fas fa-cog mr-3"></i>
                        Settings
                    </a>
                    
                    <div class="border-t border-gray-100"></div>
                    
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" 
                                class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" 
                                role="menuitem" 
                                tabindex="-1">
                            <i class="fas fa-sign-out-alt mr-3"></i>
                            Sign out
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</header>
<?php /**PATH C:\proj\jewel-pro\resources\views/layouts/topbar.blade.php ENDPATH**/ ?>