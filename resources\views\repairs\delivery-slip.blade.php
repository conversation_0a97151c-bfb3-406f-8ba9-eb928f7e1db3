<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery Slip - {{ $repair->repair_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .company-details {
            font-size: 11px;
            color: #666;
        }
        .slip-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            color: #333;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
        }
        .details-section {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .details-left, .details-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 0 10px;
        }
        .section-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .info-box {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #4CAF50;
        }
        .delivery-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 2px solid #4CAF50;
        }
        .item-details {
            background: #fff;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .item-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .item-details td {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .item-details .label {
            font-weight: bold;
            width: 30%;
            color: #333;
        }
        .payment-summary {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #ddd;
        }
        .payment-summary table {
            width: 100%;
        }
        .payment-summary td {
            padding: 5px 0;
        }
        .total-row {
            font-weight: bold;
            font-size: 14px;
            border-top: 2px solid #333;
            padding-top: 10px;
        }
        .acknowledgment {
            margin-top: 30px;
            padding: 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
        }
        .signature-section {
            margin-top: 40px;
            display: table;
            width: 100%;
        }
        .signature-left, .signature-right {
            display: table-cell;
            width: 50%;
            text-align: center;
            vertical-align: bottom;
            padding: 20px;
        }
        .signature-box {
            border: 1px solid #333;
            height: 80px;
            margin-bottom: 10px;
            background: #fafafa;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        .important-note {
            background: #ffebee;
            border: 1px solid #f44336;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #d32f2f;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Jewel Pro') }}</div>
        <div class="company-details">
            Premium Jewelry Store<br>
            123 Main Street, City, State - 123456<br>
            Phone: +91 98765 43210 | Email: <EMAIL><br>
            GSTIN: 29ABCDE1234F1Z5
        </div>
    </div>

    <div class="slip-title">ITEM DELIVERY SLIP</div>

    <div class="details-section">
        <div class="details-left">
            <div class="section-title">Customer Information</div>
            <div class="info-box">
                <strong>{{ $repair->customer->name }}</strong><br>
                Mobile: {{ $repair->customer->mobile }}<br>
                @if($repair->customer->email)
                    Email: {{ $repair->customer->email }}<br>
                @endif
                @if($repair->customer->address)
                    Address: {{ $repair->customer->address }}
                @endif
            </div>
        </div>
        <div class="details-right">
            <div class="section-title">Delivery Information</div>
            <div class="info-box">
                <strong>Repair #:</strong> {{ $repair->repair_number }}<br>
                <strong>Received Date:</strong> {{ $repair->received_date->format('d/m/Y') }}<br>
                <strong>Completed Date:</strong> {{ $repair->actual_completion_date ? $repair->actual_completion_date->format('d/m/Y') : 'N/A' }}<br>
                <strong>Delivery Date:</strong> {{ $repair->delivery_date ? $repair->delivery_date->format('d/m/Y') : date('d/m/Y') }}
            </div>
        </div>
    </div>

    <div class="delivery-info">
        <div class="section-title" style="color: #2e7d32;">✓ ITEM READY FOR DELIVERY</div>
        <p style="margin: 0; font-weight: bold;">The repaired item is now ready for collection. Please bring this delivery slip and make the payment if any balance is due.</p>
    </div>

    <div class="item-details">
        <div class="section-title">Item & Repair Details</div>
        <table>
            <tr>
                <td class="label">Item Description:</td>
                <td>{{ $repair->item_description }}</td>
            </tr>
            <tr>
                <td class="label">Repair Type:</td>
                <td>{{ ucfirst(str_replace('_', ' ', $repair->repair_type)) }}</td>
            </tr>
            <tr>
                <td class="label">Problem Description:</td>
                <td>{{ $repair->problem_description }}</td>
            </tr>
            @if($repair->work_done)
            <tr>
                <td class="label">Work Completed:</td>
                <td>{{ $repair->work_done }}</td>
            </tr>
            @endif
            @if($repair->notes)
            <tr>
                <td class="label">Additional Notes:</td>
                <td>{{ $repair->notes }}</td>
            </tr>
            @endif
        </table>
    </div>

    @if($repair->actual_cost || $repair->amount_paid)
    <div class="payment-summary">
        <div class="section-title">Payment Summary</div>
        <table>
            @if($repair->estimated_cost)
            <tr>
                <td>Original Estimate:</td>
                <td style="text-align: right;">₹{{ number_format($repair->estimated_cost, 2) }}</td>
            </tr>
            @endif
            @if($repair->actual_cost)
            <tr>
                <td>Final Repair Cost:</td>
                <td style="text-align: right;">₹{{ number_format($repair->actual_cost, 2) }}</td>
            </tr>
            @endif
            @if($repair->amount_paid)
            <tr>
                <td>Amount Already Paid:</td>
                <td style="text-align: right;">₹{{ number_format($repair->amount_paid, 2) }}</td>
            </tr>
            @endif
            @if($repair->actual_cost && $repair->amount_paid)
            <tr class="total-row">
                <td><strong>Balance Due:</strong></td>
                <td style="text-align: right;"><strong>₹{{ number_format($repair->actual_cost - $repair->amount_paid, 2) }}</strong></td>
            </tr>
            @endif
        </table>
    </div>
    @endif

    @if($repair->actual_cost && $repair->amount_paid && ($repair->actual_cost - $repair->amount_paid) > 0)
    <div class="important-note">
        <strong>IMPORTANT:</strong> A balance of ₹{{ number_format($repair->actual_cost - $repair->amount_paid, 2) }} is due. 
        Payment must be made in full before item collection.
    </div>
    @endif

    <div class="acknowledgment">
        <div class="section-title">Customer Acknowledgment</div>
        <p>I acknowledge that I have received the above-mentioned item(s) in good condition and that all repair work has been completed to my satisfaction. I confirm that all payments have been settled.</p>
    </div>

    <div class="signature-section">
        <div class="signature-left">
            <div class="signature-box"></div>
            <strong>Customer Signature</strong><br>
            <small>Date: _______________</small>
        </div>
        <div class="signature-right">
            <div class="signature-box"></div>
            <strong>Delivered By</strong><br>
            <small>Date: _______________</small>
        </div>
    </div>

    <div class="footer">
        <strong>Thank you for choosing our repair services!</strong><br>
        This delivery slip serves as proof of item collection.<br>
        For any queries, please contact us at +91 98765 43210
    </div>
</body>
</html>
