<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('old_gold_purchases', function (Blueprint $table) {
            $table->id();
            $table->string('purchase_number')->unique();
            $table->foreignId('customer_id')->constrained('customers');
            $table->string('metal_type'); // Gold, Silver
            $table->string('purity'); // 22K, 18K, etc.
            $table->decimal('gross_weight', 8, 3); // in grams
            $table->decimal('stone_weight', 8, 3)->default(0); // in grams
            $table->decimal('net_weight', 8, 3); // gross - stone weight
            $table->decimal('melting_loss_percentage', 5, 2)->default(0);
            $table->decimal('melting_loss_weight', 8, 3)->default(0);
            $table->decimal('final_weight', 8, 3); // net - melting loss
            $table->decimal('rate_per_gram', 10, 2);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('voucher_amount', 10, 2)->default(0); // store credit
            $table->decimal('cash_paid', 10, 2)->default(0);
            $table->enum('status', ['purchased', 'converted_to_voucher', 'used_in_exchange'])->default('purchased');
            $table->foreignId('used_in_sale_id')->nullable()->constrained('sales');
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('old_gold_purchases');
    }
};
