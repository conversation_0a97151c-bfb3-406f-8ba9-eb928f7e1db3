<!-- Professional Sidebar -->
<div class="relative">
    <!-- Mobile Overlay -->
    <div x-show="sidebarOpen" 
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         @click="closeSidebar()"
         class="fixed inset-0 bg-gray-900 bg-opacity-75 z-40 lg:hidden"></div>

    <!-- Sidebar -->
    <div :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'"
         class="fixed inset-y-0 left-0 z-50 w-72 main-sidebar transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 flex flex-col">
        
        <!-- Brand Header -->
        <div class="sidebar-brand px-6 py-6 border-b border-white border-opacity-10">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-gem text-white text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h1 class="text-xl font-bold text-white"><?php echo e(config('app.name', 'Jewel Pro')); ?></h1>
                    <p class="text-sm text-blue-200">Professional Edition</p>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto sidebar-scroll">
            <!-- Dashboard -->
            <a href="<?php echo e(route('dashboard')); ?>" 
               class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>">
                <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                <span>Dashboard</span>
            </a>

            <!-- Sales & Orders Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">
                    <i class="fas fa-shopping-bag mr-2"></i>Sales & Orders
                </h3>

                <a href="<?php echo e(route('sales.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('sales.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-shopping-cart w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Sales & Billing</span>
                    <span class="ml-auto bg-green-500 text-white text-xs px-2 py-1 rounded-full animate-pulse">Live</span>
                </a>

                <a href="<?php echo e(route('estimates.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('estimates.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-file-invoice w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Estimates</span>
                    <?php if(($stats['pending_estimates'] ?? 0) > 0): ?>
                        <span class="ml-auto bg-orange-500 text-white text-xs px-2 py-1 rounded-full"><?php echo e($stats['pending_estimates']); ?></span>
                    <?php endif; ?>
                </a>

                <a href="<?php echo e(route('old-gold-purchases.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('old-gold-purchases.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-exchange-alt w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Old Gold Exchange</span>
                </a>
            </div>

            <!-- Inventory Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">
                    <i class="fas fa-boxes mr-2"></i>Inventory
                </h3>

                <a href="<?php echo e(route('products.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('products.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-gem w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Products & Inventory</span>
                    <?php if(($stats['total_products'] ?? 0) > 0): ?>
                        <span class="ml-auto bg-blue-500 text-white text-xs px-2 py-1 rounded-full"><?php echo e($stats['total_products']); ?></span>
                    <?php endif; ?>
                </a>

                <a href="<?php echo e(route('barcodes.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('barcodes.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-barcode w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Barcodes & Labels</span>
                </a>
            </div>

            <!-- Services Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">
                    <i class="fas fa-concierge-bell mr-2"></i>Services
                </h3>

                <a href="<?php echo e(route('repairs.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('repairs.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-tools w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Repairs & Services</span>
                    <?php if(($stats['pending_repairs'] ?? 0) > 0): ?>
                        <span class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full"><?php echo e($stats['pending_repairs']); ?></span>
                    <?php endif; ?>
                </a>

                <a href="<?php echo e(route('saving-schemes.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('saving-schemes.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-piggy-bank w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Saving Schemes</span>
                </a>
            </div>

            <!-- Customers Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">
                    <i class="fas fa-user-friends mr-2"></i>Customers
                </h3>

                <a href="<?php echo e(route('customers.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('customers.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-users w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Customer Management</span>
                    <?php if(($stats['total_customers'] ?? 0) > 0): ?>
                        <span class="ml-auto bg-purple-500 text-white text-xs px-2 py-1 rounded-full"><?php echo e($stats['total_customers']); ?></span>
                    <?php endif; ?>
                </a>
            </div>

            <!-- Tools Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">
                    <i class="fas fa-toolbox mr-2"></i>Tools
                </h3>

                <a href="<?php echo e(route('metal-rates.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('metal-rates.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-coins w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Metal Rates</span>
                    <span class="ml-auto bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">Live</span>
                </a>

                <a href="<?php echo e(route('calculator')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('calculator*') ? 'active' : ''); ?> group">
                    <i class="fas fa-calculator w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Price Calculator</span>
                </a>
            </div>

            <!-- Analytics Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">
                    <i class="fas fa-chart-line mr-2"></i>Analytics
                </h3>

                <a href="<?php echo e(route('reports.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('reports.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-chart-bar w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Reports & Analytics</span>
                </a>
            </div>

            <!-- System Section -->
            <div class="pt-4">
                <h3 class="px-4 text-xs font-semibold text-blue-200 uppercase tracking-wider mb-3">
                    <i class="fas fa-cogs mr-2"></i>System
                </h3>

                <a href="<?php echo e(route('settings.index')); ?>"
                   class="nav-item flex items-center px-4 py-3 text-sm font-medium text-white rounded-lg <?php echo e(request()->routeIs('settings.*') ? 'active' : ''); ?> group">
                    <i class="fas fa-cog w-5 h-5 mr-3 group-hover:scale-110 transition-transform"></i>
                    <span>Settings</span>
                </a>
            </div>
        </nav>

        <!-- User Profile Section -->
        <div class="border-t border-white border-opacity-10 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white font-semibold text-sm"><?php echo e(substr(Auth::user()->name, 0, 1)); ?></span>
                    </div>
                </div>
                <div class="ml-3 flex-1 min-w-0">
                    <p class="text-sm font-medium text-white truncate"><?php echo e(Auth::user()->name); ?></p>
                    <p class="text-xs text-blue-200 truncate"><?php echo e(Auth::user()->email); ?></p>
                </div>
                <div class="ml-2">
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="text-blue-200 hover:text-white transition-colors">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\proj\jewel-pro\resources\views/layouts/components/sidebar.blade.php ENDPATH**/ ?>